# Script to update all .bat files to use auto_installer_v2.ps1
# Author: BAOPROVIP Team

$scriptDirectory = $PSScriptRoot
$batDirectory = Join-Path -Path $scriptDirectory -ChildPath "..\installers\by_department"

Write-Host "Updating all .bat files to use auto_installer_v2.ps1..." -ForegroundColor Cyan

# Get all .bat files
$batFiles = Get-ChildItem -Path $batDirectory -Filter "*.bat"

$updatedCount = 0

foreach ($batFile in $batFiles) {
    try {
        # Read current content
        $content = Get-Content -Path $batFile.FullName -Raw
        
        # Replace auto_installer.ps1 with auto_installer_v2.ps1
        $newContent = $content -replace "auto_installer\.ps1", "auto_installer_v2.ps1"
        
        # Write back to file
        $newContent | Out-File -FilePath $batFile.FullName -Encoding ASCII
        
        Write-Host "Updated: $($batFile.Name)" -ForegroundColor Green
        $updatedCount++
    }
    catch {
        Write-Host "Failed to update: $($batFile.Name) - $_" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Updated $updatedCount .bat files" -ForegroundColor Cyan
Write-Host "All files now use auto_installer_v2.ps1" -ForegroundColor Green
