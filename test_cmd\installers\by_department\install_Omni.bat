@echo off
setlocal enabledelayedexpansion

:: Set colors
for /f %%A in ('echo prompt $E ^| cmd') do set "ESC=%%A"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "CYAN=%ESC%[36m"
set "WHITE=%ESC%[37m"
set "RESET=%ESC%[0m"

title CrowdStrike Installation - Omni

echo %CYAN%========================================%RESET%
echo %CYAN%  CrowdStrike Auto Installation%RESET%
echo %CYAN%========================================%RESET%
echo.
echo %WHITE%Department: Omni%RESET%
echo %WHITE%Install Type: AV%RESET%
echo.

:: Check core script
set "CORE_SCRIPT=%~dp0..\..\core\auto_installer.ps1"
if not exist "%CORE_SCRIPT%" (
    echo %RED%ERROR: Core script not found%RESET%
    pause
    exit /b 1
)

:: Run installation
echo %BLUE%Starting installation...%RESET%
powershell -ExecutionPolicy Bypass -File "%CORE_SCRIPT%" -Department "Omni" -ForceInstallType "AV" -Silent

echo.
if %errorlevel% equ 0 (
    echo %GREEN%Installation completed successfully!%RESET%
) else (
    echo %RED%Installation failed%RESET%
)

echo.
pause
