@echo off
setlocal enabledelayedexpansion

:: Set colors for output
for /f %%A in ('echo prompt $E ^| cmd') do set "ESC=%%A"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "CYAN=%ESC%[36m"
set "WHITE=%ESC%[37m"
set "RESET=%ESC%[0m"

title CrowdStrike Quick Install - BAOPROVIP

echo %CYAN%========================================%RESET%
echo %CYAN%  CrowdStrike Quick Install Tool%RESET%
echo %CYAN%  BAOPROVIP - Common Departments%RESET%
echo %CYAN%========================================%RESET%
echo.

:: Check if PowerShell script exists
set "PS_SCRIPT=%~dp0run_auto_crowstrike.ps1"
if not exist "%PS_SCRIPT%" (
    echo %RED%ERROR: PowerShell script not found%RESET%
    goto :error_exit
)

echo %BLUE%Quick Install Options for Common Departments:%RESET%
echo.
echo %WHITE%HIGH PRIORITY (CS EDR):%RESET%
echo %WHITE%1. IT-Administration%RESET%
echo %WHITE%2. Cyber-Security%RESET%
echo %WHITE%3. Core-System%RESET%
echo %WHITE%4. Database-Management%RESET%
echo %WHITE%5. Accounting%RESET%
echo.
echo %WHITE%MEDIUM PRIORITY (CS AV):%RESET%
echo %WHITE%6. HR-Admin%RESET%
echo %WHITE%7. Marketing%RESET%
echo %WHITE%8. Customer-Service%RESET%
echo %WHITE%9. Business-Development%RESET%
echo.
echo %WHITE%OTHER OPTIONS:%RESET%
echo %WHITE%10. Full Interactive Mode%RESET%
echo %WHITE%11. List All Departments%RESET%
echo %WHITE%12. Exit%RESET%
echo.

set /p "choice=Enter your choice (1-12): "

if "%choice%"=="1" goto :install_it_admin
if "%choice%"=="2" goto :install_cyber_security
if "%choice%"=="3" goto :install_core_system
if "%choice%"=="4" goto :install_database
if "%choice%"=="5" goto :install_accounting
if "%choice%"=="6" goto :install_hr_admin
if "%choice%"=="7" goto :install_marketing
if "%choice%"=="8" goto :install_customer_service
if "%choice%"=="9" goto :install_business_dev
if "%choice%"=="10" goto :interactive
if "%choice%"=="11" goto :list_departments
if "%choice%"=="12" goto :exit
goto :invalid_choice

:install_it_admin
echo %BLUE%Installing CrowdStrike for IT-Administration (CS EDR)...%RESET%
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -Department "IT-Administration" -Silent
goto :end

:install_cyber_security
echo %BLUE%Installing CrowdStrike for Cyber-Security (CS EDR)...%RESET%
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -Department "Cyber-Security" -Silent
goto :end

:install_core_system
echo %BLUE%Installing CrowdStrike for Core-System (CS EDR)...%RESET%
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -Department "Core-System" -Silent
goto :end

:install_database
echo %BLUE%Installing CrowdStrike for Database-Management (CS EDR)...%RESET%
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -Department "Database-Management" -Silent
goto :end

:install_accounting
echo %BLUE%Installing CrowdStrike for Accounting (CS EDR)...%RESET%
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -Department "Accounting" -Silent
goto :end

:install_hr_admin
echo %BLUE%Installing CrowdStrike for HR-Admin (CS AV)...%RESET%
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -Department "HR-Admin" -Silent
goto :end

:install_marketing
echo %BLUE%Installing CrowdStrike for Marketing (CS AV)...%RESET%
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -Department "Marketing" -Silent
goto :end

:install_customer_service
echo %BLUE%Installing CrowdStrike for Customer-Service (CS AV)...%RESET%
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -Department "Customer-Service" -Silent
goto :end

:install_business_dev
echo %BLUE%Installing CrowdStrike for Business-Development (CS AV)...%RESET%
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -Department "Business-Development" -Silent
goto :end

:interactive
echo %BLUE%Starting Full Interactive Mode...%RESET%
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%"
goto :end

:list_departments
echo %BLUE%Listing All Departments...%RESET%
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -ListDepartments
pause
goto :end

:invalid_choice
echo %RED%ERROR: Invalid choice. Please enter a number between 1-12.%RESET%
pause
goto :end

:error_exit
echo %RED%Installation aborted due to errors.%RESET%
pause
exit /b 1

:exit
echo %YELLOW%Exiting...%RESET%
exit /b 0

:end
echo.
echo %GREEN%Quick install operation completed.%RESET%
echo %WHITE%Check the log file at: C:\Temp\FalconInstall_Auto.log%RESET%
echo.
pause
exit /b 0
