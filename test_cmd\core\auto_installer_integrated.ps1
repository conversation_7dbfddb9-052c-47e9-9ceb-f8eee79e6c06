# CrowdStrike Integrated Auto Installer
# This script integrates the logic from install_script.ps1 with automation
# No need for input files or redirection - everything in one place
# Author: BAOPROVIP Team
# Version: 3.0 Integrated

param(
    [Parameter(Mandatory=$true)]
    [string]$Department,
    
    [Parameter(Mandatory=$false)]
    [string]$Position = "",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("AV", "EDR", "AUTO")]
    [string]$ForceInstallType = "AUTO",
    
    [Parameter(Mandatory=$false)]
    [switch]$Silent = $false
)

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
if (-not $isAdmin) {
    try {
        $scriptPath = $MyInvocation.MyCommand.Path
        $arguments = ""
        if ($Department) { $arguments += " -Department `"$Department`"" }
        if ($Position) { $arguments += " -Position `"$Position`"" }
        if ($ForceInstallType -ne "AUTO") { $arguments += " -ForceInstallType `"$ForceInstallType`"" }
        if ($Silent) { $arguments += " -Silent" }
        
        Start-Process -FilePath "powershell.exe" -ArgumentList "-NoProfile -ExecutionPolicy Bypass -File `"$scriptPath`"$arguments" -Verb RunAs
        Write-Host "Requesting Administrator privileges..." -ForegroundColor Yellow
        exit 0
    }
    catch {
        Write-Host "Error: Cannot elevate to Administrator. Please run script as Administrator." -ForegroundColor Red
        exit 1
    }
}

# Start logging (same as original script)
Start-Transcript -Path "C:\Temp\FalconInstall.log" -Append -ErrorAction SilentlyContinue

# Constants (from original install_script.ps1)
$ProvWaitTime = "1500000" # 25 minutes

# Get script directory and installer path
$scriptDirectory = $PSScriptRoot
$installerDirectory = Join-Path -Path $scriptDirectory -ChildPath "..\FalconSensor_Windows_installer (All AV)"
$installerFilename = "FalconSensor_Windows.exe"
$installerPath = Join-Path -Path $installerDirectory -ChildPath $installerFilename

# Check if installer exists
if (-not (Test-Path $installerPath)) {
    Write-Host "Error: Installer file '$installerFilename' not found in directory '$installerDirectory'." -ForegroundColor Red
    Write-Host "Please ensure the installer file is in the correct directory." -ForegroundColor Red
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 1
}

# Load department rules
$rulesPath = Join-Path -Path $scriptDirectory -ChildPath "department_rules.json"
if (-not (Test-Path $rulesPath)) {
    Write-Host "Error: Department rules file not found at $rulesPath" -ForegroundColor Red
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 1
}

try {
    $rules = Get-Content -Path $rulesPath -Raw | ConvertFrom-Json
}
catch {
    Write-Host "Error: Failed to parse department rules: $_" -ForegroundColor Red
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 1
}

# Department mapping (EXACT copy from install_script.ps1 lines 71-108)
$groupOptions = @(
    "Board-of-Directors",           # 1
    "HR-Admin",                     # 2
    "Legal-Compliance",             # 3
    "Marketing",                    # 4
    "HN-Branch",                    # 5
    "Accounting",                   # 6
    "Fee-Control",                  # 7
    "Data-Exchange",                # 8
    "Service-Operation-Division",   # 9
    "Training",                     # 10
    "Customer-Service",             # 11
    "Account-System-Management",    # 12
    "Quality-of-Service",           # 13
    "Project-Strategy-Division",    # 14
    "Financial-Service-Project",    # 15
    "Bill-Payment-Project",         # 16
    "Business-Development",         # 17
    "Network-Development",          # 18
    "E-Commerce",                   # 19
    "Omni",                         # 20
    "Paycode",                      # 21
    "Digi-Gift",                    # 22
    "Product-Management",           # 23
    "Payoo-X-and-Biz-Solutions",   # 24
    "Web",                          # 25
    "System-Integration",           # 26
    "Core-System",                  # 27
    "Database-Management",          # 28
    "Quality-Control",              # 29
    "Business-Analysis",            # 30
    "Payoo-Plus-Digital-Transformation", # 31
    "Mobile-App",                   # 32
    "IT-Administration",            # 33
    "Technical-Operation",          # 34
    "Cyber-Security",               # 35
    "NTT",                          # 36
    "Collaborator"                  # 37
)

# Find department index
$groupChoice = -1
for ($i = 0; $i -lt $groupOptions.Count; $i++) {
    if ($groupOptions[$i] -eq $Department) {
        $groupChoice = $i + 1  # 1-based indexing
        break
    }
}

if ($groupChoice -eq -1) {
    Write-Host "Error: Department '$Department' not found in the list" -ForegroundColor Red
    Write-Host "Available departments:" -ForegroundColor Yellow
    for ($i = 0; $i -lt $groupOptions.Count; $i++) {
        Write-Host "  $($i + 1). $($groupOptions[$i])" -ForegroundColor Gray
    }
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 1
}

$groupingTags = $groupOptions[$groupChoice - 1]

# Determine install type based on rules
function Get-InstallationType {
    param($Department, $Position)
    
    # If force type is specified and not AUTO, use it
    if ($ForceInstallType -ne "AUTO") {
        return $ForceInstallType
    }
    
    # Check if position requires EDR (TeamLead and above)
    if ($Position -ne "" -and $rules.management_positions -contains $Position.ToLower()) {
        return "EDR"
    }
    
    # Check if department is in default EDR list
    if ($rules.edr_departments -contains $Department) {
        return "EDR"
    }
    
    # Check DEV departments with senior positions
    if ($rules.dev_departments -contains $Department -and $Position -ne "" -and $rules.senior_positions -contains $Position.ToLower()) {
        Write-Host "DEV department with senior position detected. Installing EDR (license permitting)." -ForegroundColor Yellow
        return "EDR"
    }
    
    # Default to AV
    return "AV"
}

$installType = Get-InstallationType -Department $Department -Position $Position

# Set CID based on install type (from original install_script.ps1 lines 52-60)
if ($installType -eq "EDR") {
    $CID = "F1E595107CDA48009C1C668EBB7A7211-83"  # CID for CS EDR
    $installChoice = 2
} else {
    $CID = "335D602035914F1CAF1319063B696DF5-D8"  # CID for CS AV
    $installChoice = 1
}

# Display installation summary
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  CrowdStrike Integrated Installation" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Department: $Department" -ForegroundColor White
Write-Host "Position: $(if ($Position) { $Position } else { 'Not specified' })" -ForegroundColor White
Write-Host "Install Type: $installType" -ForegroundColor $(if ($installType -eq "EDR") { "Red" } else { "Green" })
Write-Host "Group Choice: $groupChoice ($groupingTags)" -ForegroundColor Gray
Write-Host "Install Choice: $installChoice" -ForegroundColor Gray
Write-Host "Installer Path: $installerPath" -ForegroundColor Gray
Write-Host "Provisioning Wait Time: $ProvWaitTime ms" -ForegroundColor Gray
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Confirmation (skip in silent mode)
if (-not $Silent) {
    $confirm = Read-Host "Do you want to proceed with the installation? (Y/N)"
    if ($confirm -notmatch '^[Yy]') {
        Write-Host "Installation cancelled by user." -ForegroundColor Yellow
        Stop-Transcript -ErrorAction SilentlyContinue
        exit 0
    }
}

# Mask CID for display (same as original script)
$maskedCID = "********-$($CID.Substring($CID.Length - 2))"
$displayCommand = "`"$installerPath`" /install /quiet /norestart CID=$maskedCID ProvWaitTime=$ProvWaitTime GROUPING_TAGS=`"$groupingTags`""

Write-Host ""
Write-Host "Starting CrowdStrike installation..." -ForegroundColor Cyan
Write-Host "Running installation command:" -ForegroundColor White
Write-Host $displayCommand -ForegroundColor Gray
Write-Host ""

# Execute installation (same logic as original install_script.ps1 lines 151-172)
try {
    $startTime = Get-Date
    Write-Host "Installation started at: $($startTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Green
    
    # Run the installation (exact same command as original script)
    Start-Process -FilePath $installerPath -ArgumentList "/install /quiet /norestart CID=$CID ProvWaitTime=$ProvWaitTime GROUPING_TAGS=`"$groupingTags`"" -Wait -NoNewWindow
    
    $endTime = Get-Date
    $installDurationMs = ($endTime - $startTime).TotalMilliseconds
    $installDurationMin = [math]::Round($installDurationMs / 60000, 2)
    
    Write-Host ""
    Write-Host "Installation completed successfully!" -ForegroundColor Green
    Write-Host "Installation finished at: $($endTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Green
    Write-Host "Total installation time: $installDurationMin minutes" -ForegroundColor Green
    
    # Check if installation took 10 minutes or less (same as original)
    if ($installDurationMs -le 600000) {
        Write-Host "✓ Installation completed within 10 minutes - EXCELLENT!" -ForegroundColor Green
    } else {
        Write-Host "⚠ Installation took longer than 10 minutes - Please verify status" -ForegroundColor Yellow
    }
    
    # Check installation status
    Write-Host ""
    Write-Host "Checking installation status..." -ForegroundColor Cyan
    try {
        $service = Get-Service -Name "csagent" -ErrorAction SilentlyContinue
        if ($service -and $service.Status -eq "Running") {
            Write-Host "✓ CrowdStrike service is running successfully!" -ForegroundColor Green
        } else {
            Write-Host "⚠ CrowdStrike service status unclear. Please check manually." -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "⚠ Could not check service status. Please verify manually with: sc.exe query csagent" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "Next Steps:" -ForegroundColor Cyan
    Write-Host "1. Verify installation status: sc.exe query csagent" -ForegroundColor White
    Write-Host "2. Check service state should be: Running" -ForegroundColor White
    Write-Host "3. Review log file: C:\Temp\FalconInstall.log" -ForegroundColor White
    
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 0
}
catch {
    Write-Host ""
    Write-Host "Error during installation: $_" -ForegroundColor Red
    Write-Host "Please check the log file: C:\Temp\FalconInstall.log" -ForegroundColor Yellow
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 1
}

Write-Host ""
Write-Host "Please verify the installation status manually." -ForegroundColor Cyan
Write-Host "Use: sc.exe query csagent" -ForegroundColor White
Stop-Transcript -ErrorAction SilentlyContinue
