# CrowdStrike Automated Installation System

## 🚀 Quick Setup for New Machine

### Step 1: Copy Files
```
Copy entire test_cmd folder to target machine
Ensure FalconSensor_Windows.exe is in the installer directory
```

### Step 2: Deploy
```
Option A: Shared folder
\\server\CrowdStrike\ → Copy 61 .bat files from installers\by_department\

Option B: Local copy  
C:\CrowdStrike\ → Copy entire test_cmd folder
```

### Step 3: User Instructions
```
Regular staff: install_[Department].bat
Manager/Senior: install_[Department]_EDR.bat
Double-click → Yes (Admin) → Done
```

## 📁 File Structure

```
test_cmd/
├── core/
│   ├── auto_installer_integrated.ps1    # Main automation script
│   └── department_rules.json            # Department configuration
├── installers/
│   └── by_department/                    # 61 installer files
│       ├── install_Marketing.bat        # AV for regular staff
│       ├── install_Marketing_EDR.bat    # EDR for managers
│       └── ... (59 other files)
├── tools/
│   └── check_status.bat                 # Status checker
└── FalconSensor_Windows_installer (All AV)/
    ├── FalconSensor_Windows.exe         # CrowdStrike installer
    └── install_script.ps1               # Original script
```

## 📋 Usage Examples

```
Marketing Staff → install_Marketing.bat → AV
Marketing Manager → install_Marketing_EDR.bat → EDR  
IT Staff → install_IT-Administration.bat → EDR
HR Staff → install_HR-Admin.bat → EDR
```

## 🔧 Verification

After installation:
```
sc.exe query csagent
# Should show: STATE: 4 RUNNING

tools\check_status.bat
# For detailed status check
```

---
**Production Ready - No Testing Required**  
**Developed by BAOPROVIP Team**
