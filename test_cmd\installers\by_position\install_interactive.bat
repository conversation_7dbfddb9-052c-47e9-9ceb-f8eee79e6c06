@echo off
setlocal enabledelayedexpansion

:: Set colors
for /f %%A in ('echo prompt $E ^| cmd') do set "ESC=%%A"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "CYAN=%ESC%[36m"
set "WHITE=%ESC%[37m"
set "RESET=%ESC%[0m"

title CrowdStrike Interactive Installation

echo %CYAN%========================================%RESET%
echo %CYAN%  CrowdStrike Interactive Installation%RESET%
echo %CYAN%  Department + Position Selection%RESET%
echo %CYAN%========================================%RESET%
echo.

:: Get department
echo %YELLOW%Step 1: Enter your department name%RESET%
echo %WHITE%Examples: Marketing, HR-Admin, IT-Administration, etc.%RESET%
set /p "DEPARTMENT=Department: "

if "%DEPARTMENT%"=="" (
    echo %RED%ERROR: Department name is required%RESET%
    pause
    exit /b 1
)

echo.
:: Get position
echo %YELLOW%Step 2: Select your position%RESET%
echo %WHITE%1. Staff (Regular employee)%RESET%
echo %WHITE%2. Senior (Senior specialist)%RESET%
echo %WHITE%3. Team Lead%RESET%
echo %WHITE%4. Manager%RESET%
echo %WHITE%5. Director%RESET%
echo.

set /p "POS_CHOICE=Choose position (1-5): "

set "POSITION="
set "INSTALL_TYPE=AUTO"

if "%POS_CHOICE%"=="1" (
    set "POSITION=Staff"
    set "INSTALL_TYPE=AUTO"
)
if "%POS_CHOICE%"=="2" (
    set "POSITION=Senior"
    set "INSTALL_TYPE=AUTO"
)
if "%POS_CHOICE%"=="3" (
    set "POSITION=TeamLead"
    set "INSTALL_TYPE=EDR"
)
if "%POS_CHOICE%"=="4" (
    set "POSITION=Manager"
    set "INSTALL_TYPE=EDR"
)
if "%POS_CHOICE%"=="5" (
    set "POSITION=Director"
    set "INSTALL_TYPE=EDR"
)

if "%POSITION%"=="" (
    echo %RED%ERROR: Invalid position choice%RESET%
    pause
    exit /b 1
)

echo.
echo %WHITE%Summary:%RESET%
echo %WHITE%Department: %DEPARTMENT%%RESET%
echo %WHITE%Position: %POSITION%%RESET%
echo %WHITE%Install Type: %INSTALL_TYPE%%RESET%
echo.

set /p "CONFIRM=Proceed with installation? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo %YELLOW%Installation cancelled%RESET%
    pause
    exit /b 0
)

:: Check core script
set "CORE_SCRIPT=%~dp0..\..\core\auto_installer.ps1"
if not exist "%CORE_SCRIPT%" (
    echo %RED%ERROR: Core script not found%RESET%
    pause
    exit /b 1
)

:: Run installation
echo.
echo %BLUE%Starting installation...%RESET%
powershell -ExecutionPolicy Bypass -File "%CORE_SCRIPT%" -Department "%DEPARTMENT%" -Position "%POSITION%" -ForceInstallType "%INSTALL_TYPE%"

echo.
if %errorlevel% equ 0 (
    echo %GREEN%Installation completed successfully!%RESET%
) else (
    echo %RED%Installation failed%RESET%
)

echo.
pause
