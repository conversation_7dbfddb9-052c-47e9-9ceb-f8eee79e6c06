# Simple generator for department installer files
# Author: BAOPROVIP Team

$scriptDirectory = $PSScriptRoot
$coreDirectory = Join-Path -Path $scriptDirectory -ChildPath "..\core"
$rulesPath = Join-Path -Path $coreDirectory -ChildPath "department_rules.json"
$outputDirectory = Join-Path -Path $scriptDirectory -ChildPath "..\installers\by_department"

Write-Host "Starting generator..." -ForegroundColor Cyan

# Create output directory
if (-not (Test-Path $outputDirectory)) {
    New-Item -ItemType Directory -Path $outputDirectory -Force | Out-Null
    Write-Host "Created directory: $outputDirectory" -ForegroundColor Green
}

# Load rules
if (-not (Test-Path $rulesPath)) {
    Write-Host "Error: Rules file not found at $rulesPath" -ForegroundColor Red
    exit 1
}

$rules = Get-Content -Path $rulesPath -Raw | ConvertFrom-Json

Write-Host "Generating installer files..." -ForegroundColor Yellow

$count = 0

# Generate for each department
$departments = @(
    @{name="Board-of-Directors"; type="EDR"},
    @{name="HR-Admin"; type="EDR"},
    @{name="Legal-Compliance"; type="EDR"},
    @{name="Marketing"; type="AV"},
    @{name="HN-Branch"; type="AV"},
    @{name="Accounting"; type="EDR"},
    @{name="Fee-Control"; type="AV"},
    @{name="Data-Exchange"; type="AV"},
    @{name="Service-Operation-Division"; type="AV"},
    @{name="Training"; type="AV"},
    @{name="Customer-Service"; type="EDR"},
    @{name="Account-System-Management"; type="EDR"},
    @{name="Quality-of-Service"; type="EDR"},
    @{name="Project-Strategy-Division"; type="AV"},
    @{name="Financial-Service-Project"; type="AV"},
    @{name="Bill-Payment-Project"; type="AV"},
    @{name="Business-Development"; type="AV"},
    @{name="Network-Development"; type="AV"},
    @{name="E-Commerce"; type="AV"},
    @{name="Omni"; type="AV"},
    @{name="Paycode"; type="AV"},
    @{name="Digi-Gift"; type="EDR"},
    @{name="Product-Management"; type="AV"},
    @{name="Payoo-X-and-Biz-Solutions"; type="AV"},
    @{name="Web"; type="AV"},
    @{name="System-Integration"; type="AV"},
    @{name="Core-System"; type="EDR"},
    @{name="Database-Management"; type="EDR"},
    @{name="Quality-Control"; type="AV"},
    @{name="Business-Analysis"; type="AV"},
    @{name="Payoo-Plus-Digital-Transformation"; type="AV"},
    @{name="Mobile-App"; type="AV"},
    @{name="IT-Administration"; type="EDR"},
    @{name="Technical-Operation"; type="EDR"},
    @{name="Cyber-Security"; type="EDR"},
    @{name="NTT"; type="AV"},
    @{name="Collaborator"; type="AV"}
)

foreach ($dept in $departments) {
    $deptName = $dept.name
    $installType = $dept.type
    
    # Create safe filename
    $safeFileName = $deptName -replace '[^a-zA-Z0-9\-_]', '_'
    $fileName = "install_$safeFileName.bat"
    $filePath = Join-Path -Path $outputDirectory -ChildPath $fileName
    
    # Create batch content
    $batchContent = @"
@echo off
setlocal enabledelayedexpansion

:: Set colors
for /f %%A in ('echo prompt `$E ^| cmd') do set "ESC=%%A"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "CYAN=%ESC%[36m"
set "WHITE=%ESC%[37m"
set "RESET=%ESC%[0m"

title CrowdStrike Installation - $deptName

echo %CYAN%========================================%RESET%
echo %CYAN%  CrowdStrike Auto Installation%RESET%
echo %CYAN%========================================%RESET%
echo.
echo %WHITE%Department: $deptName%RESET%
echo %WHITE%Install Type: $installType%RESET%
echo.

:: Check core script
set "CORE_SCRIPT=%~dp0..\..\core\auto_installer.ps1"
if not exist "%CORE_SCRIPT%" (
    echo %RED%ERROR: Core script not found%RESET%
    pause
    exit /b 1
)

:: Run installation
echo %BLUE%Starting installation...%RESET%
powershell -ExecutionPolicy Bypass -File "%CORE_SCRIPT%" -Department "$deptName" -ForceInstallType "$installType" -Silent

echo.
if %errorlevel% equ 0 (
    echo %GREEN%Installation completed successfully!%RESET%
) else (
    echo %RED%Installation failed%RESET%
)

echo.
pause
"@

    # Write file
    try {
        $batchContent | Out-File -FilePath $filePath -Encoding ASCII
        Write-Host "Generated: $fileName" -ForegroundColor Green
        $count++
    }
    catch {
        Write-Host "Failed: $fileName - $_" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Generated $count installer files" -ForegroundColor Cyan
Write-Host "Location: $outputDirectory" -ForegroundColor Gray
