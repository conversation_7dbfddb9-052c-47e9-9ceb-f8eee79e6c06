# CrowdStrike Auto Installation Script for Departments
# Requires running with Administrator privileges
# Author: BAOPROVIP Team
# Version: 1.0

param(
    [string]$Department = "",
    [string]$InstallType = "",
    [switch]$Silent = $false,
    [switch]$ListDepartments = $false
)

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
if (-not $isAdmin) {
    try {
        $scriptPath = $MyInvocation.MyCommand.Path
        $arguments = ""
        if ($Department) { $arguments += " -Department `"$Department`"" }
        if ($InstallType) { $arguments += " -InstallType `"$InstallType`"" }
        if ($Silent) { $arguments += " -Silent" }
        if ($ListDepartments) { $arguments += " -ListDepartments" }
        
        Start-Process -FilePath "powershell.exe" -ArgumentList "-NoProfile -ExecutionPolicy Bypass -File `"$scriptPath`"$arguments" -Verb RunAs
        Write-Host "Requesting Administrator privileges..." -ForegroundColor Yellow
        exit 0
    }
    catch {
        Write-Host "Error: Cannot elevate to Administrator. Please run script as Administrator." -ForegroundColor Red
        exit 1
    }
}

# Start logging
$logPath = "C:\Temp\FalconInstall_Auto.log"
if (-not (Test-Path "C:\Temp")) {
    New-Item -ItemType Directory -Path "C:\Temp" -Force | Out-Null
}
Start-Transcript -Path $logPath -Append -ErrorAction SilentlyContinue

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  CrowdStrike Auto Installation Tool" -ForegroundColor Cyan
Write-Host "  BAOPROVIP - Department Automation" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Get script directory and config file path
$scriptDirectory = $PSScriptRoot
$configPath = Join-Path -Path $scriptDirectory -ChildPath "department_config.json"

# Load configuration
if (-not (Test-Path $configPath)) {
    Write-Host "Error: Configuration file not found at $configPath" -ForegroundColor Red
    Write-Host "Please ensure department_config.json exists in the script directory." -ForegroundColor Red
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 1
}

try {
    $config = Get-Content -Path $configPath -Raw | ConvertFrom-Json
}
catch {
    Write-Host "Error: Failed to parse configuration file: $_" -ForegroundColor Red
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 1
}

# Function to display available departments
function Show-Departments {
    Write-Host "Available Departments:" -ForegroundColor Green
    Write-Host "=====================" -ForegroundColor Green
    
    $departments = $config.departments.PSObject.Properties | Sort-Object Name
    $counter = 1
    
    foreach ($dept in $departments) {
        $deptInfo = $dept.Value
        $installType = $deptInfo.install_type
        $priority = $deptInfo.priority
        $description = $deptInfo.description
        
        $priorityColor = switch ($priority) {
            "high" { "Red" }
            "medium" { "Yellow" }
            "low" { "Green" }
            default { "White" }
        }
        
        Write-Host ("{0,2}. {1}" -f $counter, $dept.Name) -ForegroundColor White
        Write-Host ("    Description: {0}" -f $description) -ForegroundColor Gray
        Write-Host ("    Install Type: {0}" -f $installType) -ForegroundColor Cyan
        Write-Host ("    Priority: {0}" -f $priority.ToUpper()) -ForegroundColor $priorityColor
        Write-Host ""
        $counter++
    }
}

# Function to get user choice
function Get-UserChoice {
    param(
        [string]$Prompt,
        [array]$Options
    )
    
    do {
        Write-Host $Prompt -ForegroundColor Yellow
        for ($i = 0; $i -lt $Options.Count; $i++) {
            Write-Host ("{0}. {1}" -f ($i + 1), $Options[$i]) -ForegroundColor White
        }
        
        $choice = Read-Host "Enter your choice (1-$($Options.Count))"
        
        if ($choice -match '^\d+$' -and [int]$choice -ge 1 -and [int]$choice -le $Options.Count) {
            return [int]$choice
        }
        else {
            Write-Host "Invalid choice. Please enter a number between 1 and $($Options.Count)." -ForegroundColor Red
        }
    } while ($true)
}

# Handle list departments parameter
if ($ListDepartments) {
    Show-Departments
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 0
}

# Interactive mode if no parameters provided
if (-not $Department -and -not $Silent) {
    Write-Host "Running in Interactive Mode" -ForegroundColor Green
    Write-Host "===========================" -ForegroundColor Green
    Write-Host ""
    
    # Show departments and let user choose
    Show-Departments
    
    $departments = $config.departments.PSObject.Properties | Sort-Object Name
    $departmentNames = $departments | ForEach-Object { $_.Name }
    
    $deptChoice = Get-UserChoice -Prompt "`nSelect department for installation:" -Options $departmentNames
    $Department = $departmentNames[$deptChoice - 1]
}

# Validate department
if (-not $config.departments.$Department) {
    Write-Host "Error: Department '$Department' not found in configuration." -ForegroundColor Red
    Write-Host "Use -ListDepartments to see available departments." -ForegroundColor Yellow
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 1
}

$deptConfig = $config.departments.$Department
$selectedInstallType = if ($InstallType) { $InstallType } else { $deptConfig.install_type }

# Validate install type
if (-not $config.install_types.$selectedInstallType) {
    Write-Host "Error: Install type '$selectedInstallType' not supported." -ForegroundColor Red
    Write-Host "Supported types: CS AV, CS EDR" -ForegroundColor Yellow
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 1
}

# Get installation details
$installConfig = $config.install_types.$selectedInstallType
$CID = $installConfig.cid
$installerFilename = $config.settings.installer_filename
$ProvWaitTime = $config.settings.prov_wait_time

# Check if installer exists
$installerPath = Join-Path -Path $scriptDirectory -ChildPath "FalconSensor_Windows_installer (All AV)\$installerFilename"
if (-not (Test-Path $installerPath)) {
    Write-Host "Error: Installer file not found at $installerPath" -ForegroundColor Red
    Write-Host "Please ensure the installer file exists in the correct directory." -ForegroundColor Red
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 1
}

# Display installation summary
Write-Host "Installation Summary:" -ForegroundColor Green
Write-Host "====================" -ForegroundColor Green
Write-Host "Department: $($deptConfig.description) ($Department)" -ForegroundColor White
Write-Host "Install Type: $selectedInstallType ($($installConfig.description))" -ForegroundColor White
Write-Host "Priority: $($deptConfig.priority.ToUpper())" -ForegroundColor $(
    switch ($deptConfig.priority) {
        "high" { "Red" }
        "medium" { "Yellow" }
        "low" { "Green" }
        default { "White" }
    }
)
Write-Host "Installer: $installerPath" -ForegroundColor White
Write-Host "Provisioning Wait Time: $ProvWaitTime ms" -ForegroundColor White
Write-Host ""

# Confirmation (skip in silent mode)
if (-not $Silent) {
    $confirm = Read-Host "Do you want to proceed with the installation? (Y/N)"
    if ($confirm -notmatch '^[Yy]') {
        Write-Host "Installation cancelled by user." -ForegroundColor Yellow
        Stop-Transcript -ErrorAction SilentlyContinue
        exit 0
    }
}

# Mask CID for display (show only last 2 characters)
$maskedCID = "********-$($CID.Substring($CID.Length - 2))"
$displayCommand = "`"$installerPath`" /install /quiet /norestart CID=$maskedCID ProvWaitTime=$ProvWaitTime GROUPING_TAGS=`"$Department`""

Write-Host "Starting CrowdStrike installation..." -ForegroundColor Cyan
Write-Host "Command: $displayCommand" -ForegroundColor White
Write-Host ""

try {
    $startTime = Get-Date
    Write-Host "Installation started at: $($startTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Green
    
    # Run the installation
    Start-Process -FilePath $installerPath -ArgumentList "/install /quiet /norestart CID=$CID ProvWaitTime=$ProvWaitTime GROUPING_TAGS=`"$Department`"" -Wait -NoNewWindow
    
    $endTime = Get-Date
    $installDurationMs = ($endTime - $startTime).TotalMilliseconds
    $installDurationMin = [math]::Round($installDurationMs / 60000, 2)
    
    Write-Host ""
    Write-Host "Installation completed successfully!" -ForegroundColor Green
    Write-Host "Installation finished at: $($endTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Green
    Write-Host "Total installation time: $installDurationMin minutes" -ForegroundColor Green
    
    # Check if installation took 10 minutes or less
    if ($installDurationMs -le 600000) {
        Write-Host "✓ Installation completed within 10 minutes - EXCELLENT!" -ForegroundColor Green
    }
    else {
        Write-Host "⚠ Installation took longer than 10 minutes - Please verify status" -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "Next Steps:" -ForegroundColor Cyan
    Write-Host "1. Verify installation status: sc.exe query csagent" -ForegroundColor White
    Write-Host "2. Check service state should be: Running" -ForegroundColor White
    Write-Host "3. Review log file: $logPath" -ForegroundColor White
    
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 0
}
catch {
    Write-Host ""
    Write-Host "Error during installation: $_" -ForegroundColor Red
    Write-Host "Please check the log file: $logPath" -ForegroundColor Yellow
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 1
}

Write-Host ""
Write-Host "Please verify the installation status manually." -ForegroundColor Cyan
Write-Host "Use: sc.exe query csagent" -ForegroundColor White
Stop-Transcript -ErrorAction SilentlyContinue
