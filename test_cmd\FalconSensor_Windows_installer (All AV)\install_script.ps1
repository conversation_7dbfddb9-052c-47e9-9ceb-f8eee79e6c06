# Script to install CrowdStrike Falcon Sensor (CS AV or CS EDR) with user options
# Requires running with Administrator privileges
# Runs in the directory containing the script

$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
if (-not $isAdmin) {
    try {
        $scriptPath = $MyInvocation.MyCommand.Path
        Start-Process -FilePath "powershell.exe" -ArgumentList "-NoProfile -ExecutionPolicy Bypass -File `"$scriptPath`"" -Verb RunAs
        Write-Host "Requesting Administrator privileges..." -ForegroundColor Yellow
        exit 0
    }
    catch {
        Write-Host "Error: Cannot elevate to Administrator. Please run script as Administrator." -ForegroundColor Red
        exit 1
    }
}

Start-Transcript -Path "C:\Temp\FalconInstall.log" -Append -ErrorAction SilentlyContinue #Path Log for Troubleshooting


$ProvWaitTime = "1500000" # 25 minutes

$scriptDirectory = $PSScriptRoot 

function Get-UserChoice {
    param (
        [string]$Prompt,
        [array]$Options
    )
    Write-Host $Prompt -ForegroundColor Cyan
    for ($i = 0; $i -lt $Options.Length; $i++) {
        Write-Host "$($i + 1). $($Options[$i])" -ForegroundColor Yellow
    }
    do {
        $choice = Read-Host "Enter your choice (1-$($Options.Length))"
        # Validate input is a positive integer within range
        if ($choice -match '^[1-9]\d*$' -and [int]$choice -ge 1 -and [int]$choice -le $Options.Length) {
            return [int]$choice
        }
        Write-Host "Invalid input. Please enter a number between 1 and $($Options.Length)." -ForegroundColor Red
    } while ($true)
}

$proceed = $false
do {
    $installOptions = @("CS AV", "CS EDR")
    $installPrompt = "`nSelect the installation type:"
    $installChoice = Get-UserChoice -Prompt $installPrompt -Options $installOptions

    switch ($installChoice) {
        1 { 
            $installerFilename = ".\FalconSensor_Windows.exe" 
            $CID = "335D602035914F1CAF1319063B696DF5-D8"  # CID for CS AV 
        }
        2 { 
            $installerFilename = ".\FalconSensor_Windows.exe" 
            $CID = "F1E595107CDA48009C1C668EBB7A7211-83"  # CID for CS EDR 
        }
    }

    $installerPath = Join-Path -Path $scriptDirectory -ChildPath $installerFilename

    if (-not (Test-Path $installerPath)) {
        Write-Host "Error: Installer file '$installerFilename' not found in directory '$scriptDirectory'." -ForegroundColor Red
        Write-Host "Please ensure the installer file is in the same directory as the script." -ForegroundColor Red
        Stop-Transcript -ErrorAction SilentlyContinue
        exit 1
    }

    $groupOptions = @(
        "Board-of-Directors",
        "HR-Admin",
        "Legal-Compliance",
        "Marketing",
        "HN-Branch",
        "Accounting",
        "Fee-Control",
        "Data-Exchange",
        "Service-Operation-Division",
        "Training",
        "Customer-Service",
        "Account-System-Management",
        "Quality-of-Service",
        "Project-Strategy-Division",
        "Financial-Service-Project",
        "Bill-Payment-Project",
        "Business-Development",
        "Network-Development",
        "E-Commerce",
        "Omni",
        "Paycode",
        "Digi-Gift",
        "Product-Management",
        "Payoo-X-and-Biz-Solutions",
        "Web",
        "System-Integration",
        "Core-System",
        "Database-Management",
        "Quality-Control",
        "Business-Analysis",
        "Payoo-Plus-Digital-Transformation",
        "Mobile-App",
        "IT-Administration",
        "Technical-Operation",
        "Cyber-Security",
        "NTT",
        "Collaborator"
    )
    $groupPrompt = "`nSelect the group for deployment:"
    $groupChoice = Get-UserChoice -Prompt $groupPrompt -Options $groupOptions

    $groupingTags = $groupOptions[$groupChoice - 1]

    Write-Host "`n=== Installation Confirmation ===" -ForegroundColor Green
    Write-Host "Installer Type: $($installOptions[$installChoice - 1])"
    Write-Host "Installer File: $installerPath"
    Write-Host "Group: $groupingTags"
    Write-Host "Provisioning Wait Time: $ProvWaitTime ms"
    Write-Host "================================" -ForegroundColor Green

    $confirmOptions = @("Continue", "Reselect", "Exit")
    $confirmPrompt = "`nWhat would you like to do?"
    $confirmChoice = Get-UserChoice -Prompt $confirmPrompt -Options $confirmOptions

    switch ($confirmChoice) {
        1 { # Continue
            $proceed = $true # Set flag to proceed with installation
        }
        2 { # Reselect
            Write-Host "Returning to selection options..." -ForegroundColor Yellow
            $proceed = $false # Continue looping to reselect
        }
        3 { # Exit
            Write-Host "Exiting script..." -ForegroundColor Yellow
            Stop-Transcript -ErrorAction SilentlyContinue
            exit 0
        }
    }
} while (-not $proceed)

# Mask CID for display (show only last 2 characters)
$maskedCID = "********-$($CID.Substring($CID.Length - 2))"
$displayCommand = "`"$installerPath`" /install /quiet /norestart CID=$maskedCID ProvWaitTime=$ProvWaitTime GROUPING_TAGS=`"$groupingTags`""
Write-Host "`nRunning installation command:" -ForegroundColor Cyan
Write-Host $displayCommand -ForegroundColor White


#$command = "`"$installerPath`" /install /quiet /norestart CID=$CID APP_PROXYNAME=$ProxyName APP_PROXYPORT=$ProxyPort ProvWaitTime=$ProvWaitTime GROUPING_TAGS=`"$groupingTags`""

try {
    
    $startTime = Get-Date
   
    Start-Process -FilePath $installerPath -ArgumentList "/install /quiet /norestart CID=$CID ProvWaitTime=$ProvWaitTime GROUPING_TAGS=`"$groupingTags`"" -Wait -NoNewWindow
    $endTime = Get-Date
    $installDurationMs = ($endTime - $startTime).TotalMilliseconds

    Write-Host "`nInstallation completed successfully." -ForegroundColor Green

    # Check if installation took 10 minutes or less
    if ($installDurationMs -le 600000) {
        Write-Host "Cài đặt hoàn tất trong vòng 10 phút" -ForegroundColor Green
        Stop-Transcript -ErrorAction SilentlyContinue
        exit 0
    }
}
catch {
    Write-Host "Error during installation: $_" -ForegroundColor Red
    Stop-Transcript -ErrorAction SilentlyContinue
    exit 1
}

Write-Host "`nPlease check the installation status" -ForegroundColor Cyan
Stop-Transcript -ErrorAction SilentlyContinue
Pause