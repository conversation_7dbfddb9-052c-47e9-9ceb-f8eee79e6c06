# Generator for EDR variant files for AV departments
# Creates additional EDR installer files for departments that normally get AV
# Author: BAOPROVIP Team

$scriptDirectory = $PSScriptRoot
$coreDirectory = Join-Path -Path $scriptDirectory -ChildPath "..\core"
$rulesPath = Join-Path -Path $coreDirectory -ChildPath "department_rules.json"
$outputDirectory = Join-Path -Path $scriptDirectory -ChildPath "..\installers\by_department"

Write-Host "Generating EDR variant files for AV departments..." -ForegroundColor Cyan

# Load rules
if (-not (Test-Path $rulesPath)) {
    Write-Host "Error: Rules file not found at $rulesPath" -ForegroundColor Red
    exit 1
}

$rules = Get-Content -Path $rulesPath -Raw | ConvertFrom-Json

# Define AV departments that need EDR variants
$avDepartments = @(
    @{name="Marketing"; description="Phòng Marketing"},
    @{name="HN-Branch"; description="Chi Nhánh Hà Nộ<PERSON>"},
    @{name="Fee-Control"; description="<PERSON>òng <PERSON>"},
    @{name="Data-Exchange"; description="Phòng Trao Đổi Dữ Liệu"},
    @{name="Service-Operation-Division"; description="Khối Vận Hành Dịch Vụ"},
    @{name="Training"; description="Phòng Đào Tạo"},
    @{name="Project-Strategy-Division"; description="Khối Chiến Lược Dự Án"},
    @{name="Financial-Service-Project"; description="Dự Án Dịch Vụ Tài Chính"},
    @{name="Bill-Payment-Project"; description="Dự Án Thanh Toán Hóa Đơn"},
    @{name="Business-Development"; description="Phòng Phát Triển Kinh Doanh"},
    @{name="Network-Development"; description="Phòng Phát Triển Mạng Lưới"},
    @{name="E-Commerce"; description="Phòng Thương Mại Điện Tử"},
    @{name="Omni"; description="Phòng Omni"},
    @{name="Paycode"; description="Phòng Paycode"},
    @{name="Product-Management"; description="Phòng Quản Lý Sản Phẩm"},
    @{name="Payoo-X-and-Biz-Solutions"; description="Phòng Payoo-X và Giải Pháp Doanh Nghiệp"},
    @{name="Web"; description="Phòng Web"},
    @{name="System-Integration"; description="Phòng Tích Hợp Hệ Thống"},
    @{name="Quality-Control"; description="Phòng Kiểm Soát Chất Lượng"},
    @{name="Business-Analysis"; description="Phòng Phân Tích Kinh Doanh"},
    @{name="Payoo-Plus-Digital-Transformation"; description="Phòng Payoo-Plus và Chuyển Đổi Số"},
    @{name="Mobile-App"; description="Phòng Ứng Dụng Di Động"},
    @{name="NTT"; description="Phòng NTT"},
    @{name="Collaborator"; description="Cộng Tác Viên"}
)

$count = 0

foreach ($dept in $avDepartments) {
    $deptName = $dept.name
    $deptDescription = $dept.description
    
    # Create safe filename
    $safeFileName = $deptName -replace '[^a-zA-Z0-9\-_]', '_'
    $fileName = "install_$safeFileName`_EDR.bat"
    $filePath = Join-Path -Path $outputDirectory -ChildPath $fileName
    
    # Create batch content
    $batchContent = @"
@echo off
setlocal enabledelayedexpansion

:: Set colors
for /f %%A in ('echo prompt `$E ^| cmd') do set "ESC=%%A"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "CYAN=%ESC%[36m"
set "WHITE=%ESC%[37m"
set "RESET=%ESC%[0m"

title CrowdStrike EDR Installation - $deptName

echo %CYAN%========================================%RESET%
echo %CYAN%  CrowdStrike EDR Installation%RESET%
echo %CYAN%  $deptDescription%RESET%
echo %CYAN%========================================%RESET%
echo.
echo %WHITE%Department: $deptName%RESET%
echo %WHITE%Description: $deptDescription%RESET%
echo %WHITE%Install Type: EDR (Enhanced Security)%RESET%
echo %WHITE%Target Users: Manager, Team Lead, Senior Staff%RESET%
echo.

echo %YELLOW%This is the EDR version for senior positions in $deptName%RESET%
echo %WHITE%- Use this if you are: Manager, Team Lead, Senior, or Specialist Lv3%RESET%
echo %WHITE%- For regular staff, use: install_$safeFileName.bat (AV version)%RESET%
echo.

set /p "CONFIRM=Are you a senior position (Manager/TeamLead/Senior)? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo.
    echo %YELLOW%For regular staff positions, please use the AV version:%RESET%
    echo %WHITE%File: install_$safeFileName.bat%RESET%
    echo.
    pause
    exit /b 0
)

:: Check core script
set "CORE_SCRIPT=%~dp0..\..\core\auto_installer.ps1"
if not exist "%CORE_SCRIPT%" (
    echo %RED%ERROR: Core script not found at %CORE_SCRIPT%%RESET%
    pause
    exit /b 1
)

:: Run installation
echo.
echo %BLUE%Starting EDR installation for $deptName...%RESET%
powershell -ExecutionPolicy Bypass -File "%CORE_SCRIPT%" -Department "$deptName" -ForceInstallType "EDR" -Silent

echo.
if %errorlevel% equ 0 (
    echo %GREEN%EDR Installation completed successfully!%RESET%
    echo %WHITE%Department: $deptName%RESET%
    echo %WHITE%Type: EDR (Enhanced Security)%RESET%
) else (
    echo %RED%Installation failed%RESET%
    echo %YELLOW%Please check the log file for details%RESET%
)

echo.
echo %CYAN%Next steps:%RESET%
echo %WHITE%1. Verify service: sc.exe query csagent%RESET%
echo %WHITE%2. Check log: C:\Temp\FalconInstall.log%RESET%
echo.

pause
"@

    # Write file
    try {
        $batchContent | Out-File -FilePath $filePath -Encoding ASCII
        Write-Host "Generated: $fileName" -ForegroundColor Green
        $count++
    }
    catch {
        Write-Host "Failed: $fileName - $_" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Generated $count EDR variant files" -ForegroundColor Cyan
Write-Host "Location: $outputDirectory" -ForegroundColor Gray

# Create summary of what was generated
Write-Host ""
Write-Host "EDR variants created for these AV departments:" -ForegroundColor Yellow
foreach ($dept in $avDepartments) {
    $safeFileName = $dept.name -replace '[^a-zA-Z0-9\-_]', '_'
    Write-Host "  - install_$safeFileName`_EDR.bat ($($dept.name))" -ForegroundColor White
}

Write-Host ""
Write-Host "Usage:" -ForegroundColor Cyan
Write-Host "- Regular staff: Use install_[Department].bat (AV)" -ForegroundColor White
Write-Host "- Senior positions: Use install_[Department]_EDR.bat (EDR)" -ForegroundColor White
