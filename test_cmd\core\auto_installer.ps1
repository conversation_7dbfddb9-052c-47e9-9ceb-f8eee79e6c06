# CrowdStrike Automated Installer
# This script automates the original install_script.ps1 by providing predefined choices
# Author: BAOPROVIP Team
# Version: 1.0

param(
    [Parameter(Mandatory=$true)]
    [string]$Department,
    
    [Parameter(Mandatory=$false)]
    [string]$Position = "",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("AV", "EDR", "AUTO")]
    [string]$ForceInstallType = "AUTO",
    
    [Parameter(Mandatory=$false)]
    [switch]$Silent = $false
)

# Get script directory and paths
$scriptDirectory = $PSScriptRoot
$rulesPath = Join-Path -Path $scriptDirectory -ChildPath "department_rules.json"
$originalInstallerPath = Join-Path -Path $scriptDirectory -ChildPath "..\FalconSensor_Windows_installer (All AV)\install_script.ps1"

# Check if rules file exists
if (-not (Test-Path $rulesPath)) {
    Write-Host "Error: Department rules file not found at $rulesPath" -ForegroundColor Red
    exit 1
}

# Check if original installer exists
if (-not (Test-Path $originalInstallerPath)) {
    Write-Host "Error: Original installer script not found at $originalInstallerPath" -ForegroundColor Red
    exit 1
}

# Load department rules
try {
    $rules = Get-Content -Path $rulesPath -Raw | ConvertFrom-Json
}
catch {
    Write-Host "Error: Failed to parse department rules: $_" -ForegroundColor Red
    exit 1
}

# Function to determine install type based on department and position
function Get-InstallationType {
    param($Department, $Position)
    
    # If force type is specified and not AUTO, use it
    if ($ForceInstallType -ne "AUTO") {
        return $ForceInstallType
    }
    
    # Check if position requires EDR (TeamLead and above)
    if ($Position -ne "" -and $rules.management_positions -contains $Position.ToLower()) {
        return "EDR"
    }
    
    # Check if department is in default EDR list
    if ($rules.edr_departments -contains $Department) {
        return "EDR"
    }
    
    # Check DEV departments with senior positions
    if ($rules.dev_departments -contains $Department -and $Position -ne "" -and $rules.senior_positions -contains $Position.ToLower()) {
        # TODO: Add license checking logic here
        Write-Host "DEV department with senior position detected. Installing EDR (license permitting)." -ForegroundColor Yellow
        return "EDR"
    }
    
    # Default to AV
    return "AV"
}

# Function to get department choice number from the original script's array
function Get-DepartmentChoiceNumber {
    param($Department)
    
    # This array must match exactly with the original install_script.ps1
    $groupOptions = @(
        "Board-of-Directors",
        "HR-Admin", 
        "Legal-Compliance",
        "Marketing",
        "HN-Branch",
        "Accounting",
        "Fee-Control",
        "Data-Exchange",
        "Service-Operation-Division",
        "Training",
        "Customer-Service",
        "Account-System-Management",
        "Quality-of-Service",
        "Project-Strategy-Division",
        "Financial-Service-Project",
        "Bill-Payment-Project",
        "Business-Development",
        "Network-Development",
        "E-Commerce",
        "Omni",
        "Paycode",
        "Digi-Gift",
        "Product-Management",
        "Payoo-X-and-Biz-Solutions",
        "Web",
        "System-Integration",
        "Core-System",
        "Database-Management",
        "Quality-Control",
        "Business-Analysis",
        "Payoo-Plus-Digital-Transformation",
        "Mobile-App",
        "IT-Administration",
        "Technical-Operation",
        "Cyber-Security",
        "NTT",
        "Collaborator"
    )
    
    for ($i = 0; $i -lt $groupOptions.Count; $i++) {
        if ($groupOptions[$i] -eq $Department) {
            return $i + 1  # Original script uses 1-based indexing
        }
    }
    
    Write-Host "Error: Department '$Department' not found in the list" -ForegroundColor Red
    return -1
}

# Validate department
$deptChoice = Get-DepartmentChoiceNumber -Department $Department
if ($deptChoice -eq -1) {
    Write-Host "Available departments:" -ForegroundColor Yellow
    $rules.department_mapping.PSObject.Properties.Name | Sort-Object | ForEach-Object { Write-Host "  - $_" }
    exit 1
}

# Determine install type
$installType = Get-InstallationType -Department $Department -Position $Position
$installChoice = if ($installType -eq "EDR") { 2 } else { 1 }

# Display installation summary
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  CrowdStrike Automated Installation" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Department: $Department" -ForegroundColor White
Write-Host "Position: $(if ($Position) { $Position } else { 'Not specified' })" -ForegroundColor White
Write-Host "Install Type: $installType" -ForegroundColor $(if ($installType -eq "EDR") { "Red" } else { "Green" })

if ($rules.department_mapping.$Department) {
    Write-Host "Reason: $($rules.department_mapping.$Department.reason)" -ForegroundColor Gray
}

Write-Host "Department Choice: $deptChoice" -ForegroundColor Gray
Write-Host "Install Choice: $installChoice" -ForegroundColor Gray
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Confirmation (skip in silent mode)
if (-not $Silent) {
    $confirm = Read-Host "Do you want to proceed with the installation? (Y/N)"
    if ($confirm -notmatch '^[Yy]') {
        Write-Host "Installation cancelled by user." -ForegroundColor Yellow
        exit 0
    }
}

# Create input file for the original script
$inputFile = Join-Path -Path $env:TEMP -ChildPath "crowdstrike_input.txt"
$inputContent = @"
$installChoice
$deptChoice
1
"@

try {
    $inputContent | Out-File -FilePath $inputFile -Encoding ASCII
    Write-Host "Created input file: $inputFile" -ForegroundColor Green
    
    # Change to the installer directory
    $installerDir = Split-Path -Parent $originalInstallerPath
    Push-Location $installerDir
    
    Write-Host "Starting CrowdStrike installation..." -ForegroundColor Cyan
    Write-Host "Running: powershell -ExecutionPolicy Bypass .\install_script.ps1 < $inputFile" -ForegroundColor Gray
    
    # Run the original installer with input redirection
    $process = Start-Process -FilePath "powershell.exe" -ArgumentList "-ExecutionPolicy", "Bypass", "-File", ".\install_script.ps1" -RedirectStandardInput $inputFile -Wait -PassThru -NoNewWindow
    
    Pop-Location
    
    if ($process.ExitCode -eq 0) {
        Write-Host ""
        Write-Host "Installation completed successfully!" -ForegroundColor Green
        Write-Host "Department: $Department" -ForegroundColor White
        Write-Host "Install Type: $installType" -ForegroundColor $(if ($installType -eq "EDR") { "Red" } else { "Green" })
        
        # Check installation status
        Write-Host ""
        Write-Host "Checking installation status..." -ForegroundColor Cyan
        try {
            $service = Get-Service -Name "csagent" -ErrorAction SilentlyContinue
            if ($service -and $service.Status -eq "Running") {
                Write-Host "✓ CrowdStrike service is running successfully!" -ForegroundColor Green
            } else {
                Write-Host "⚠ CrowdStrike service status unclear. Please check manually." -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "⚠ Could not check service status. Please verify manually with: sc.exe query csagent" -ForegroundColor Yellow
        }
    } else {
        Write-Host ""
        Write-Host "Installation may have failed. Exit code: $($process.ExitCode)" -ForegroundColor Red
        Write-Host "Please check the installation log for details." -ForegroundColor Yellow
    }
}
catch {
    Write-Host "Error during installation: $_" -ForegroundColor Red
    exit 1
}
finally {
    # Clean up input file
    if (Test-Path $inputFile) {
        Remove-Item $inputFile -Force -ErrorAction SilentlyContinue
    }
}

Write-Host ""
Write-Host "Installation process completed." -ForegroundColor Cyan
Write-Host "Log file location: C:\Temp\FalconInstall.log" -ForegroundColor Gray
