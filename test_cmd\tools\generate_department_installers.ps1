# Generator script to create individual installer files for each department
# This script reads the department rules and creates .bat files for each department
# Author: BAOPROVIP Team

$scriptDirectory = $PSScriptRoot
$coreDirectory = Join-Path -Path $scriptDirectory -ChildPath "..\core"
$rulesPath = Join-Path -Path $coreDirectory -ChildPath "department_rules.json"
$outputDirectory = Join-Path -Path $scriptDirectory -ChildPath "..\installers\by_department"

# Create output directory if it doesn't exist
if (-not (Test-Path $outputDirectory)) {
    New-Item -ItemType Directory -Path $outputDirectory -Force | Out-Null
    Write-Host "Created directory: $outputDirectory" -ForegroundColor Green
}

# Load department rules
if (-not (Test-Path $rulesPath)) {
    Write-Host "Error: Department rules file not found at $rulesPath" -ForegroundColor Red
    exit 1
}

try {
    $rules = Get-Content -Path $rulesPath -Raw | ConvertFrom-Json
}
catch {
    Write-Host "Error: Failed to parse department rules: $_" -ForegroundColor Red
    exit 1
}

Write-Host "Generating department-specific installer files..." -ForegroundColor Cyan
Write-Host "Output directory: $outputDirectory" -ForegroundColor Gray
Write-Host ""

$generatedCount = 0

# Generate installer for each department
foreach ($department in $rules.department_mapping.PSObject.Properties) {
    $deptName = $department.Name
    $deptInfo = $department.Value
    $installType = $deptInfo.install_type
    $reason = $deptInfo.reason

    # Create safe filename (replace special characters)
    $safeFileName = $deptName -replace '[^a-zA-Z0-9\-_]', '_'
    $fileName = "install_$safeFileName.bat"
    $filePath = Join-Path -Path $outputDirectory -ChildPath $fileName

    # Determine colors based on install type
    $typeColor = if ($installType -eq "EDR") { "Red" } else { "Green" }
    $typeDescription = if ($installType -eq "EDR") { "Endpoint Detection and Response" } else { "Antivirus" }

    # Create batch file content
    $batchContent = @"
@echo off
setlocal enabledelayedexpansion

:: Set colors for output
for /f %%A in ('echo prompt $E ^| cmd') do set "ESC=%%A"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "CYAN=%ESC%[36m"
set "WHITE=%ESC%[37m"
set "RESET=%ESC%[0m"

title CrowdStrike Installation - $deptName

echo %CYAN%========================================%RESET%
echo %CYAN%  CrowdStrike Auto Installation%RESET%
echo %CYAN%========================================%RESET%
echo.
echo %WHITE%Department: $deptName%RESET%
echo %WHITE%Install Type: %$(if ($installType -eq "EDR") { "RED" } else { "GREEN" })%$installType ($typeDescription)%RESET%
echo %WHITE%Reason: $reason%RESET%
echo.

:: Check if core installer exists
set "CORE_SCRIPT=%~dp0..\..\core\auto_installer.ps1"
if not exist "%CORE_SCRIPT%" (
    echo %RED%ERROR: Core installer script not found at %CORE_SCRIPT%%RESET%
    echo %RED%Please ensure the core auto_installer.ps1 exists.%RESET%
    echo.
    pause
    exit /b 1
)

:: Run the installation
echo %BLUE%Starting installation for $deptName...%RESET%
echo.

powershell -ExecutionPolicy Bypass -File "%CORE_SCRIPT%" -Department "$deptName" -ForceInstallType "$installType" -Silent

set "EXIT_CODE=%errorlevel%"

echo.
if %EXIT_CODE% equ 0 (
    echo %GREEN%Installation completed successfully!%RESET%
    echo %WHITE%Department: $deptName%RESET%
    echo %WHITE%Type: $installType%RESET%
) else (
    echo %RED%Installation failed with exit code %EXIT_CODE%%RESET%
    echo %YELLOW%Please check the log file for details.%RESET%
)

echo.
echo %CYAN%Next steps:%RESET%
echo %WHITE%1. Verify service status: sc.exe query csagent%RESET%
echo %WHITE%2. Check log file: C:\Temp\FalconInstall.log%RESET%
echo.

pause
exit /b %EXIT_CODE%
"@

    # Write the batch file
    try {
        $batchContent | Out-File -FilePath $filePath -Encoding ASCII
        Write-Host "✓ Generated: $fileName" -ForegroundColor Green
        $generatedCount++
    }
    catch {
        Write-Host "✗ Failed to generate: $fileName - $_" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Generation completed!" -ForegroundColor Cyan
Write-Host "Generated $generatedCount installer files in: $outputDirectory" -ForegroundColor Green
Write-Host ""

# Generate a summary file
$summaryPath = Join-Path -Path $outputDirectory -ChildPath "_DEPARTMENT_LIST.txt"
$summaryLines = @()
$summaryLines += "CrowdStrike Department Installer Files"
$summaryLines += "Generated on: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
$summaryLines += "Total files: $generatedCount"
$summaryLines += ""
$summaryLines += "EDR DEPARTMENTS (High Security):"

foreach ($department in $rules.department_mapping.PSObject.Properties) {
    if ($department.Value.install_type -eq "EDR") {
        $safeFileName = $department.Name -replace '[^a-zA-Z0-9\-_]', '_'
        $summaryLines += "  - install_$safeFileName.bat ($($department.Name))"
    }
}

$summaryLines += ""
$summaryLines += "AV DEPARTMENTS (Standard Security):"

foreach ($department in $rules.department_mapping.PSObject.Properties) {
    if ($department.Value.install_type -eq "AV") {
        $safeFileName = $department.Name -replace '[^a-zA-Z0-9\-_]', '_'
        $summaryLines += "  - install_$safeFileName.bat ($($department.Name))"
    }
}

$summaryLines += ""
$summaryLines += "USAGE:"
$summaryLines += "1. Navigate to the by_department folder"
$summaryLines += "2. Double-click the installer file for your department"
$summaryLines += "3. The installation will run automatically with the correct settings"
$summaryLines += "4. Check the log file at C:\Temp\FalconInstall.log for details"
$summaryLines += ""
$summaryLines += "NOTES:"
$summaryLines += "- EDR provides advanced threat detection and response"
$summaryLines += "- AV provides standard antivirus protection"
$summaryLines += "- All installations are logged for audit purposes"
$summaryLines += "- Contact IT support if you encounter any issues"

try {
    $summaryLines | Out-File -FilePath $summaryPath -Encoding UTF8
    Write-Host "✓ Generated summary file: _DEPARTMENT_LIST.txt" -ForegroundColor Green
}
catch {
    Write-Host "✗ Failed to generate summary file: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "All files generated successfully!" -ForegroundColor Green
Write-Host "You can now distribute the appropriate installer files to each department." -ForegroundColor Cyan
