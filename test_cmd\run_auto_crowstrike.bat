@echo off
setlocal enabledelayedexpansion

:: Set colors for output
for /f %%A in ('echo prompt $E ^| cmd') do set "ESC=%%A"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "CYAN=%ESC%[36m"
set "WHITE=%ESC%[37m"
set "RESET=%ESC%[0m"

title CrowdStrike Auto Installation - BAOPROVIP

echo %CYAN%========================================%RESET%
echo %CYAN%  CrowdStrike Auto Installation Tool%RESET%
echo %CYAN%  BAOPROVIP - Department Automation%RESET%
echo %CYAN%========================================%RESET%
echo.

:: Check if PowerShell script exists
set "PS_SCRIPT=%~dp0run_auto_crowstrike.ps1"
if not exist "%PS_SCRIPT%" (
    echo %RED%ERROR: PowerShell script not found at %PS_SCRIPT%%RESET%
    echo %RED%Please ensure run_auto_crowstrike.ps1 exists in the same directory.%RESET%
    echo.
    goto :error_exit
)

:: Check if config file exists
set "CONFIG_FILE=%~dp0department_config.json"
if not exist "%CONFIG_FILE%" (
    echo %RED%ERROR: Configuration file not found at %CONFIG_FILE%%RESET%
    echo %RED%Please ensure department_config.json exists in the same directory.%RESET%
    echo.
    goto :error_exit
)

:: Check if installer directory exists
set "INSTALLER_DIR=%~dp0FalconSensor_Windows_installer (All AV)"
if not exist "%INSTALLER_DIR%" (
    echo %RED%ERROR: Installer directory not found at %INSTALLER_DIR%%RESET%
    echo %RED%Please ensure the FalconSensor installer directory exists.%RESET%
    echo.
    goto :error_exit
)

:: Display menu options
echo %BLUE%Available Options:%RESET%
echo %WHITE%1. Interactive Installation (Choose department from list)%RESET%
echo %WHITE%2. List All Departments%RESET%
echo %WHITE%3. Quick Install - High Priority Departments%RESET%
echo %WHITE%4. Quick Install - Medium Priority Departments%RESET%
echo %WHITE%5. Quick Install - Low Priority Departments%RESET%
echo %WHITE%6. Custom Installation (Specify department)%RESET%
echo %WHITE%7. Exit%RESET%
echo.

set /p "choice=Enter your choice (1-7): "

if "%choice%"=="1" goto :interactive
if "%choice%"=="2" goto :list_departments
if "%choice%"=="3" goto :high_priority
if "%choice%"=="4" goto :medium_priority
if "%choice%"=="5" goto :low_priority
if "%choice%"=="6" goto :custom
if "%choice%"=="7" goto :exit
goto :invalid_choice

:interactive
echo %BLUE%Starting Interactive Installation...%RESET%
echo.
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%"
goto :end

:list_departments
echo %BLUE%Listing All Departments...%RESET%
echo.
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -ListDepartments
pause
goto :end

:high_priority
echo %BLUE%Quick Install - High Priority Departments%RESET%
echo %YELLOW%This will show only high priority departments for selection%RESET%
echo.
echo %WHITE%High Priority Departments typically include:%RESET%
echo %WHITE%- Board-of-Directors, Legal-Compliance, Accounting%RESET%
echo %WHITE%- Cyber-Security, IT-Administration, Core-System%RESET%
echo %WHITE%- Database-Management, Quality-Control, etc.%RESET%
echo.
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%"
goto :end

:medium_priority
echo %BLUE%Quick Install - Medium Priority Departments%RESET%
echo %YELLOW%This will show medium priority departments for selection%RESET%
echo.
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%"
goto :end

:low_priority
echo %BLUE%Quick Install - Low Priority Departments%RESET%
echo %YELLOW%This will show low priority departments for selection%RESET%
echo.
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%"
goto :end

:custom
echo %BLUE%Custom Installation%RESET%
echo.
set /p "dept_name=Enter department name (exact match): "
if "%dept_name%"=="" (
    echo %RED%ERROR: Department name cannot be empty%RESET%
    goto :error_exit
)

echo %BLUE%Installing for department: %dept_name%%RESET%
powershell -ExecutionPolicy Bypass -File "%PS_SCRIPT%" -Department "%dept_name%"
goto :end

:invalid_choice
echo %RED%ERROR: Invalid choice. Please enter a number between 1-7.%RESET%
echo.
pause
goto :end

:error_exit
echo.
echo %RED%Installation aborted due to errors.%RESET%
echo %YELLOW%Please check the requirements and try again.%RESET%
echo.
pause
exit /b 1

:exit
echo %YELLOW%Exiting...%RESET%
exit /b 0

:end
echo.
echo %GREEN%Operation completed.%RESET%
echo %WHITE%Check the log file at: C:\Temp\FalconInstall_Auto.log%RESET%
echo.
pause
exit /b 0
