@echo off
setlocal enabledelayedexpansion

:: Set colors for output
for /f %%A in ('echo prompt $E ^| cmd') do set "ESC=%%A"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "CYAN=%ESC%[36m"
set "WHITE=%ESC%[37m"
set "RESET=%ESC%[0m"

title CrowdStrike Installation Status Check

echo %CYAN%========================================%RESET%
echo %CYAN%  CrowdStrike Installation Status%RESET%
echo %CYAN%  BAOPROVIP - Status Checker%RESET%
echo %CYAN%========================================%RESET%
echo.

echo %BLUE%Checking CrowdStrike Falcon Sensor status...%RESET%
echo.

:: Check if csagent service exists and its status
echo %WHITE%1. Checking csagent service status:%RESET%
sc.exe query csagent >nul 2>&1
if %errorlevel% equ 0 (
    echo %GREEN%✓ CrowdStrike service found%RESET%
    echo.
    sc.exe query csagent
    echo.
    
    :: Check if service is running
    for /f "tokens=3" %%a in ('sc.exe query csagent ^| findstr "STATE"') do (
        if "%%a"=="RUNNING" (
            echo %GREEN%✓ Service Status: RUNNING - Installation successful!%RESET%
        ) else (
            echo %YELLOW%⚠ Service Status: %%a - Service not running%RESET%
        )
    )
) else (
    echo %RED%✗ CrowdStrike service not found - Installation may have failed%RESET%
)

echo.
echo %WHITE%2. Checking CrowdStrike processes:%RESET%
tasklist /fi "imagename eq CSFalconService.exe" 2>nul | findstr "CSFalconService.exe" >nul
if %errorlevel% equ 0 (
    echo %GREEN%✓ CSFalconService.exe is running%RESET%
) else (
    echo %YELLOW%⚠ CSFalconService.exe not found%RESET%
)

tasklist /fi "imagename eq CSFalconContainer.exe" 2>nul | findstr "CSFalconContainer.exe" >nul
if %errorlevel% equ 0 (
    echo %GREEN%✓ CSFalconContainer.exe is running%RESET%
) else (
    echo %YELLOW%⚠ CSFalconContainer.exe not found%RESET%
)

echo.
echo %WHITE%3. Checking installation directory:%RESET%
if exist "C:\Program Files\CrowdStrike" (
    echo %GREEN%✓ CrowdStrike installation directory found%RESET%
    dir "C:\Program Files\CrowdStrike" /b 2>nul | findstr /i "falcon" >nul
    if %errorlevel% equ 0 (
        echo %GREEN%✓ Falcon files detected%RESET%
    )
) else (
    echo %YELLOW%⚠ CrowdStrike installation directory not found%RESET%
)

echo.
echo %WHITE%4. Checking log files:%RESET%
if exist "C:\Temp\FalconInstall.log" (
    echo %GREEN%✓ Installation log found%RESET%
    echo %WHITE%  Location: C:\Temp\FalconInstall.log%RESET%
    
    :: Show last few lines of log
    echo %WHITE%  Last 5 lines of log:%RESET%
    powershell -Command "Get-Content 'C:\Temp\FalconInstall.log' -Tail 5 | ForEach-Object { Write-Host '    ' $_ -ForegroundColor Gray }" 2>nul
) else (
    echo %YELLOW%⚠ Installation log not found%RESET%
)

echo.
echo %WHITE%5. Network connectivity test:%RESET%
ping -n 1 ******* >nul 2>&1
if %errorlevel% equ 0 (
    echo %GREEN%✓ Internet connectivity available%RESET%
) else (
    echo %YELLOW%⚠ Internet connectivity issue detected%RESET%
)

echo.
echo %CYAN%========================================%RESET%
echo %CYAN%           SUMMARY REPORT%RESET%
echo %CYAN%========================================%RESET%

:: Generate summary
set "service_ok=false"
set "process_ok=false"
set "files_ok=false"

sc.exe query csagent >nul 2>&1
if %errorlevel% equ 0 (
    for /f "tokens=3" %%a in ('sc.exe query csagent ^| findstr "STATE"') do (
        if "%%a"=="RUNNING" set "service_ok=true"
    )
)

tasklist /fi "imagename eq CSFalconService.exe" 2>nul | findstr "CSFalconService.exe" >nul
if %errorlevel% equ 0 set "process_ok=true"

if exist "C:\Program Files\CrowdStrike" set "files_ok=true"

if "%service_ok%"=="true" if "%process_ok%"=="true" if "%files_ok%"=="true" (
    echo %GREEN%✓ OVERALL STATUS: INSTALLATION SUCCESSFUL%RESET%
    echo %GREEN%  CrowdStrike Falcon Sensor is properly installed and running%RESET%
) else (
    echo %YELLOW%⚠ OVERALL STATUS: INSTALLATION INCOMPLETE OR FAILED%RESET%
    echo %WHITE%  Please check the issues above and consider reinstalling%RESET%
)

echo.
echo %WHITE%Troubleshooting commands:%RESET%
echo %WHITE%- Restart service: sc.exe stop csagent ^&^& sc.exe start csagent%RESET%
echo %WHITE%- Check detailed status: sc.exe query csagent%RESET%
echo %WHITE%- View installation logs: notepad C:\Temp\FalconInstall.log%RESET%
echo %WHITE%- Contact IT support if issues persist%RESET%

echo.
pause
