# Script to update all .bat files to use auto_installer_integrated.ps1
# Author: BAOPROVIP Team

$scriptDirectory = $PSScriptRoot
$batDirectory = Join-Path -Path $scriptDirectory -ChildPath "..\installers\by_department"

Write-Host "Updating all .bat files to use auto_installer_integrated.ps1..." -ForegroundColor Cyan

# Get all .bat files
$batFiles = Get-ChildItem -Path $batDirectory -Filter "*.bat"

$updatedCount = 0

foreach ($batFile in $batFiles) {
    try {
        # Read current content
        $content = Get-Content -Path $batFile.FullName -Raw

        # Replace auto_installer_v2.ps1 with auto_installer_integrated.ps1
        $newContent = $content -replace "auto_installer_v2\.ps1", "auto_installer_integrated.ps1"

        # Write back to file
        $newContent | Out-File -FilePath $batFile.FullName -Encoding ASCII

        Write-Host "Updated: $($batFile.Name)" -ForegroundColor Green
        $updatedCount++
    }
    catch {
        Write-Host "Failed to update: $($batFile.Name) - $_" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Updated $updatedCount .bat files" -ForegroundColor Cyan
Write-Host "All files now use auto_installer_integrated.ps1" -ForegroundColor Green
Write-Host ""
Write-Host "Benefits of integrated version:" -ForegroundColor Yellow
Write-Host "- No temporary input files needed" -ForegroundColor White
Write-Host "- No redirection complexity" -ForegroundColor White
Write-Host "- Direct execution of installation logic" -ForegroundColor White
Write-Host "- Same exact behavior as original install_script.ps1" -ForegroundColor White
Write-Host "- Cleaner, more reliable automation" -ForegroundColor White
