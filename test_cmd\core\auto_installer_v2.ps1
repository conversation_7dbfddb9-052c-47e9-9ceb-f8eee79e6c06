# CrowdStrike Automated Installer v2
# This script automates the original install_script.ps1 by providing predefined input
# Author: BAOPROVIP Team
# Version: 2.0

param(
    [Parameter(Mandatory=$true)]
    [string]$Department,
    
    [Parameter(Mandatory=$false)]
    [string]$Position = "",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("AV", "EDR", "AUTO")]
    [string]$ForceInstallType = "AUTO",
    
    [Parameter(Mandatory=$false)]
    [switch]$Silent = $false
)

# Check if running as Administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
if (-not $isAdmin) {
    try {
        $scriptPath = $MyInvocation.MyCommand.Path
        $arguments = ""
        if ($Department) { $arguments += " -Department `"$Department`"" }
        if ($Position) { $arguments += " -Position `"$Position`"" }
        if ($ForceInstallType -ne "AUTO") { $arguments += " -ForceInstallType `"$ForceInstallType`"" }
        if ($Silent) { $arguments += " -Silent" }
        
        Start-Process -FilePath "powershell.exe" -ArgumentList "-NoProfile -ExecutionPolicy Bypass -File `"$scriptPath`"$arguments" -Verb RunAs
        Write-Host "Requesting Administrator privileges..." -ForegroundColor Yellow
        exit 0
    }
    catch {
        Write-Host "Error: Cannot elevate to Administrator. Please run script as Administrator." -ForegroundColor Red
        exit 1
    }
}

# Get script directory and paths
$scriptDirectory = $PSScriptRoot
$rulesPath = Join-Path -Path $scriptDirectory -ChildPath "department_rules.json"
$originalInstallerPath = Join-Path -Path $scriptDirectory -ChildPath "..\FalconSensor_Windows_installer (All AV)\install_script.ps1"

# Check if rules file exists
if (-not (Test-Path $rulesPath)) {
    Write-Host "Error: Department rules file not found at $rulesPath" -ForegroundColor Red
    exit 1
}

# Check if original installer exists
if (-not (Test-Path $originalInstallerPath)) {
    Write-Host "Error: Original installer script not found at $originalInstallerPath" -ForegroundColor Red
    exit 1
}

# Load department rules
try {
    $rules = Get-Content -Path $rulesPath -Raw | ConvertFrom-Json
}
catch {
    Write-Host "Error: Failed to parse department rules: $_" -ForegroundColor Red
    exit 1
}

# Function to determine install type based on department and position
function Get-InstallationType {
    param($Department, $Position)
    
    # If force type is specified and not AUTO, use it
    if ($ForceInstallType -ne "AUTO") {
        return $ForceInstallType
    }
    
    # Check if department is in default EDR list
    if ($rules.edr_departments -contains $Department) {
        return "EDR"
    }
    
    # Check DEV departments with senior positions
    if ($rules.dev_departments -contains $Department -and $Position -ne "" -and $rules.senior_positions -contains $Position.ToLower()) {
        Write-Host "DEV department with senior position detected. Installing EDR (license permitting)." -ForegroundColor Yellow
        return "EDR"
    }
    
    # Default to AV
    return "AV"
}

# Function to get department choice number - MUST match install_script.ps1 exactly
function Get-DepartmentChoiceNumber {
    param($Department)
    
    # This array MUST match exactly with install_script.ps1 lines 71-108
    $groupOptions = @(
        "Board-of-Directors",           # 1
        "HR-Admin",                     # 2
        "Legal-Compliance",             # 3
        "Marketing",                    # 4
        "HN-Branch",                    # 5
        "Accounting",                   # 6
        "Fee-Control",                  # 7
        "Data-Exchange",                # 8
        "Service-Operation-Division",   # 9
        "Training",                     # 10
        "Customer-Service",             # 11
        "Account-System-Management",    # 12
        "Quality-of-Service",           # 13
        "Project-Strategy-Division",    # 14
        "Financial-Service-Project",    # 15
        "Bill-Payment-Project",         # 16
        "Business-Development",         # 17
        "Network-Development",          # 18
        "E-Commerce",                   # 19
        "Omni",                         # 20
        "Paycode",                      # 21
        "Digi-Gift",                    # 22
        "Product-Management",           # 23
        "Payoo-X-and-Biz-Solutions",   # 24
        "Web",                          # 25
        "System-Integration",           # 26
        "Core-System",                  # 27
        "Database-Management",          # 28
        "Quality-Control",              # 29
        "Business-Analysis",            # 30
        "Payoo-Plus-Digital-Transformation", # 31
        "Mobile-App",                   # 32
        "IT-Administration",            # 33
        "Technical-Operation",          # 34
        "Cyber-Security",               # 35
        "NTT",                          # 36
        "Collaborator"                  # 37
    )
    
    for ($i = 0; $i -lt $groupOptions.Count; $i++) {
        if ($groupOptions[$i] -eq $Department) {
            return $i + 1  # Original script uses 1-based indexing
        }
    }
    
    Write-Host "Error: Department '$Department' not found in the list" -ForegroundColor Red
    Write-Host "Available departments:" -ForegroundColor Yellow
    for ($i = 0; $i -lt $groupOptions.Count; $i++) {
        Write-Host "  $($i + 1). $($groupOptions[$i])" -ForegroundColor Gray
    }
    return -1
}

# Validate department
$deptChoice = Get-DepartmentChoiceNumber -Department $Department
if ($deptChoice -eq -1) {
    exit 1
}

# Determine install type
$installType = Get-InstallationType -Department $Department -Position $Position
$installChoice = if ($installType -eq "EDR") { 2 } else { 1 }

# Display installation summary
Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  CrowdStrike Automated Installation" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Department: $Department" -ForegroundColor White
Write-Host "Position: $(if ($Position) { $Position } else { 'Not specified' })" -ForegroundColor White
Write-Host "Install Type: $installType" -ForegroundColor $(if ($installType -eq "EDR") { "Red" } else { "Green" })
Write-Host "Department Choice: $deptChoice" -ForegroundColor Gray
Write-Host "Install Choice: $installChoice" -ForegroundColor Gray
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Confirmation (skip in silent mode)
if (-not $Silent) {
    $confirm = Read-Host "Do you want to proceed with the installation? (Y/N)"
    if ($confirm -notmatch '^[Yy]') {
        Write-Host "Installation cancelled by user." -ForegroundColor Yellow
        exit 0
    }
}

# Create input sequence for the original script
# Input sequence: InstallChoice -> DepartmentChoice -> Continue (1)
$inputSequence = @(
    $installChoice.ToString(),  # 1 for AV, 2 for EDR
    $deptChoice.ToString(),     # Department number (1-37)
    "1"                         # Continue with installation
)

# Create input file
$inputFile = Join-Path -Path $env:TEMP -ChildPath "crowdstrike_input_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"

try {
    # Write input sequence to file
    $inputSequence | Out-File -FilePath $inputFile -Encoding ASCII
    Write-Host "Created input file: $inputFile" -ForegroundColor Green
    Write-Host "Input sequence: $($inputSequence -join ', ')" -ForegroundColor Gray
    
    # Change to the installer directory
    $installerDir = Split-Path -Parent $originalInstallerPath
    Push-Location $installerDir
    
    Write-Host ""
    Write-Host "Starting CrowdStrike installation..." -ForegroundColor Cyan
    Write-Host "Running original install_script.ps1 with automated input..." -ForegroundColor Gray
    Write-Host ""
    
    # Run the original installer with input redirection
    $process = Start-Process -FilePath "powershell.exe" -ArgumentList "-ExecutionPolicy", "Bypass", "-File", ".\install_script.ps1" -RedirectStandardInput $inputFile -Wait -PassThru -NoNewWindow
    
    Pop-Location
    
    if ($process.ExitCode -eq 0) {
        Write-Host ""
        Write-Host "Installation completed successfully!" -ForegroundColor Green
        Write-Host "Department: $Department" -ForegroundColor White
        Write-Host "Install Type: $installType" -ForegroundColor $(if ($installType -eq "EDR") { "Red" } else { "Green" })
        
        # Check installation status
        Write-Host ""
        Write-Host "Checking installation status..." -ForegroundColor Cyan
        try {
            $service = Get-Service -Name "csagent" -ErrorAction SilentlyContinue
            if ($service -and $service.Status -eq "Running") {
                Write-Host "✓ CrowdStrike service is running successfully!" -ForegroundColor Green
            } else {
                Write-Host "⚠ CrowdStrike service status unclear. Please check manually." -ForegroundColor Yellow
            }
        }
        catch {
            Write-Host "⚠ Could not check service status. Please verify manually with: sc.exe query csagent" -ForegroundColor Yellow
        }
    } else {
        Write-Host ""
        Write-Host "Installation may have failed. Exit code: $($process.ExitCode)" -ForegroundColor Red
        Write-Host "Please check the installation log for details." -ForegroundColor Yellow
    }
}
catch {
    Write-Host "Error during installation: $_" -ForegroundColor Red
    exit 1
}
finally {
    # Clean up input file
    if (Test-Path $inputFile) {
        Remove-Item $inputFile -Force -ErrorAction SilentlyContinue
    }
}

Write-Host ""
Write-Host "Installation process completed." -ForegroundColor Cyan
Write-Host "Log file location: C:\Temp\FalconInstall.log" -ForegroundColor Gray
