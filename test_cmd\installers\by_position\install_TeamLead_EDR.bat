@echo off
setlocal enabledelayedexpansion

:: Set colors
for /f %%A in ('echo prompt $E ^| cmd') do set "ESC=%%A"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "CYAN=%ESC%[36m"
set "WHITE=%ESC%[37m"
set "RESET=%ESC%[0m"

title CrowdStrike EDR Installation - Team Lead

echo %CYAN%========================================%RESET%
echo %CYAN%  CrowdStrike EDR Installation%RESET%
echo %CYAN%  Position-Based Installation%RESET%
echo %CYAN%========================================%RESET%
echo.
echo %WHITE%Position: Team Lead%RESET%
echo %WHITE%Description: Team Lead (Any Department)%RESET%
echo %WHITE%Install Type: EDR (Forced)%RESET%
echo %WHITE%Reason: Management position requires EDR%RESET%
echo.

:: Get department input
echo %YELLOW%Please enter your department name:%RESET%
echo %WHITE%Examples: Marketing, HR-Admin, Business-Development, etc.%RESET%
set /p "DEPARTMENT=Department: "

if "%DEPARTMENT%"=="" (
    echo %RED%ERROR: Department name is required%RESET%
    pause
    exit /b 1
)

echo.
echo %WHITE%You entered: %DEPARTMENT%%RESET%
echo %WHITE%Position: Team Lead%RESET%
echo %WHITE%Install Type: EDR%RESET%
echo.

set /p "CONFIRM=Is this correct? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo %YELLOW%Installation cancelled%RESET%
    pause
    exit /b 0
)

:: Check core script
set "CORE_SCRIPT=%~dp0..\..\core\auto_installer.ps1"
if not exist "%CORE_SCRIPT%" (
    echo %RED%ERROR: Core script not found at %CORE_SCRIPT%%RESET%
    pause
    exit /b 1
)

:: Run installation
echo.
echo %BLUE%Starting EDR installation for Team Lead...%RESET%
powershell -ExecutionPolicy Bypass -File "%CORE_SCRIPT%" -Department "%DEPARTMENT%" -Position "TeamLead" -ForceInstallType "EDR"

echo.
if %errorlevel% equ 0 (
    echo %GREEN%EDR Installation completed successfully!%RESET%
    echo %WHITE%Position: Team Lead%RESET%
    echo %WHITE%Department: %DEPARTMENT%%RESET%
    echo %WHITE%Type: EDR%RESET%
) else (
    echo %RED%Installation failed%RESET%
    echo %YELLOW%Please check the log file for details%RESET%
)

echo.
echo %CYAN%Next steps:%RESET%
echo %WHITE%1. Verify service: sc.exe query csagent%RESET%
echo %WHITE%2. Check log: C:\Temp\FalconInstall.log%RESET%
echo.

pause
