# Generator for position-based installer files
# This creates installers for senior positions that need EDR regardless of department
# Author: BAOPROVIP Team

$scriptDirectory = $PSScriptRoot
$outputDirectory = Join-Path -Path $scriptDirectory -ChildPath "..\installers\by_position"

Write-Host "Generating position-based installer files..." -ForegroundColor Cyan

# Create output directory
if (-not (Test-Path $outputDirectory)) {
    New-Item -ItemType Directory -Path $outputDirectory -Force | Out-Null
    Write-Host "Created directory: $outputDirectory" -ForegroundColor Green
}

# Define positions that require EDR
$positions = @(
    @{
        name = "TeamLead"
        display = "Team Lead"
        description = "Team Lead (Any Department)"
        reason = "Management position requires EDR"
    },
    @{
        name = "Manager"
        display = "Manager"
        description = "Manager (Any Department)"
        reason = "Management position requires EDR"
    },
    @{
        name = "Director"
        display = "Director"
        description = "Director (Any Department)"
        reason = "Management position requires EDR"
    },
    @{
        name = "Senior"
        display = "Senior"
        description = "Senior Staff (Any Department)"
        reason = "Senior position may require EDR"
    },
    @{
        name = "Specialist_Lv3"
        display = "Specialist Lv3"
        description = "Specialist Level 3 (Any Department)"
        reason = "Senior specialist may require EDR"
    }
)

$count = 0

foreach ($position in $positions) {
    $posName = $position.name
    $posDisplay = $position.display
    $posDescription = $position.description
    $reason = $position.reason
    
    $fileName = "install_$posName`_EDR.bat"
    $filePath = Join-Path -Path $outputDirectory -ChildPath $fileName
    
    # Create batch content
    $batchContent = @"
@echo off
setlocal enabledelayedexpansion

:: Set colors
for /f %%A in ('echo prompt `$E ^| cmd') do set "ESC=%%A"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "CYAN=%ESC%[36m"
set "WHITE=%ESC%[37m"
set "RESET=%ESC%[0m"

title CrowdStrike EDR Installation - $posDisplay

echo %CYAN%========================================%RESET%
echo %CYAN%  CrowdStrike EDR Installation%RESET%
echo %CYAN%  Position-Based Installation%RESET%
echo %CYAN%========================================%RESET%
echo.
echo %WHITE%Position: $posDisplay%RESET%
echo %WHITE%Description: $posDescription%RESET%
echo %WHITE%Install Type: EDR (Forced)%RESET%
echo %WHITE%Reason: $reason%RESET%
echo.

:: Get department input
echo %YELLOW%Please enter your department name:%RESET%
echo %WHITE%Examples: Marketing, HR-Admin, Business-Development, etc.%RESET%
set /p "DEPARTMENT=Department: "

if "%DEPARTMENT%"=="" (
    echo %RED%ERROR: Department name is required%RESET%
    pause
    exit /b 1
)

echo.
echo %WHITE%You entered: %DEPARTMENT%%RESET%
echo %WHITE%Position: $posDisplay%RESET%
echo %WHITE%Install Type: EDR%RESET%
echo.

set /p "CONFIRM=Is this correct? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo %YELLOW%Installation cancelled%RESET%
    pause
    exit /b 0
)

:: Check core script
set "CORE_SCRIPT=%~dp0..\..\core\auto_installer.ps1"
if not exist "%CORE_SCRIPT%" (
    echo %RED%ERROR: Core script not found at %CORE_SCRIPT%%RESET%
    pause
    exit /b 1
)

:: Run installation
echo.
echo %BLUE%Starting EDR installation for $posDisplay...%RESET%
powershell -ExecutionPolicy Bypass -File "%CORE_SCRIPT%" -Department "%DEPARTMENT%" -Position "$posName" -ForceInstallType "EDR"

echo.
if %errorlevel% equ 0 (
    echo %GREEN%EDR Installation completed successfully!%RESET%
    echo %WHITE%Position: $posDisplay%RESET%
    echo %WHITE%Department: %DEPARTMENT%%RESET%
    echo %WHITE%Type: EDR%RESET%
) else (
    echo %RED%Installation failed%RESET%
    echo %YELLOW%Please check the log file for details%RESET%
)

echo.
echo %CYAN%Next steps:%RESET%
echo %WHITE%1. Verify service: sc.exe query csagent%RESET%
echo %WHITE%2. Check log: C:\Temp\FalconInstall.log%RESET%
echo.

pause
"@

    # Write file
    try {
        $batchContent | Out-File -FilePath $filePath -Encoding ASCII
        Write-Host "Generated: $fileName" -ForegroundColor Green
        $count++
    }
    catch {
        Write-Host "Failed: $fileName - $_" -ForegroundColor Red
    }
}

# Create interactive installer
$interactiveFile = Join-Path -Path $outputDirectory -ChildPath "install_interactive.bat"
$interactiveContent = @"
@echo off
setlocal enabledelayedexpansion

:: Set colors
for /f %%A in ('echo prompt `$E ^| cmd') do set "ESC=%%A"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "CYAN=%ESC%[36m"
set "WHITE=%ESC%[37m"
set "RESET=%ESC%[0m"

title CrowdStrike Interactive Installation

echo %CYAN%========================================%RESET%
echo %CYAN%  CrowdStrike Interactive Installation%RESET%
echo %CYAN%  Department + Position Selection%RESET%
echo %CYAN%========================================%RESET%
echo.

:: Get department
echo %YELLOW%Step 1: Enter your department name%RESET%
echo %WHITE%Examples: Marketing, HR-Admin, IT-Administration, etc.%RESET%
set /p "DEPARTMENT=Department: "

if "%DEPARTMENT%"=="" (
    echo %RED%ERROR: Department name is required%RESET%
    pause
    exit /b 1
)

echo.
:: Get position
echo %YELLOW%Step 2: Select your position%RESET%
echo %WHITE%1. Staff (Regular employee)%RESET%
echo %WHITE%2. Senior (Senior specialist)%RESET%
echo %WHITE%3. Team Lead%RESET%
echo %WHITE%4. Manager%RESET%
echo %WHITE%5. Director%RESET%
echo.

set /p "POS_CHOICE=Choose position (1-5): "

set "POSITION="
set "INSTALL_TYPE=AUTO"

if "%POS_CHOICE%"=="1" (
    set "POSITION=Staff"
    set "INSTALL_TYPE=AUTO"
)
if "%POS_CHOICE%"=="2" (
    set "POSITION=Senior"
    set "INSTALL_TYPE=AUTO"
)
if "%POS_CHOICE%"=="3" (
    set "POSITION=TeamLead"
    set "INSTALL_TYPE=EDR"
)
if "%POS_CHOICE%"=="4" (
    set "POSITION=Manager"
    set "INSTALL_TYPE=EDR"
)
if "%POS_CHOICE%"=="5" (
    set "POSITION=Director"
    set "INSTALL_TYPE=EDR"
)

if "%POSITION%"=="" (
    echo %RED%ERROR: Invalid position choice%RESET%
    pause
    exit /b 1
)

echo.
echo %WHITE%Summary:%RESET%
echo %WHITE%Department: %DEPARTMENT%%RESET%
echo %WHITE%Position: %POSITION%%RESET%
echo %WHITE%Install Type: %INSTALL_TYPE%%RESET%
echo.

set /p "CONFIRM=Proceed with installation? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo %YELLOW%Installation cancelled%RESET%
    pause
    exit /b 0
)

:: Check core script
set "CORE_SCRIPT=%~dp0..\..\core\auto_installer.ps1"
if not exist "%CORE_SCRIPT%" (
    echo %RED%ERROR: Core script not found%RESET%
    pause
    exit /b 1
)

:: Run installation
echo.
echo %BLUE%Starting installation...%RESET%
powershell -ExecutionPolicy Bypass -File "%CORE_SCRIPT%" -Department "%DEPARTMENT%" -Position "%POSITION%" -ForceInstallType "%INSTALL_TYPE%"

echo.
if %errorlevel% equ 0 (
    echo %GREEN%Installation completed successfully!%RESET%
) else (
    echo %RED%Installation failed%RESET%
)

echo.
pause
"@

try {
    $interactiveContent | Out-File -FilePath $interactiveFile -Encoding ASCII
    Write-Host "Generated: install_interactive.bat" -ForegroundColor Green
    $count++
}
catch {
    Write-Host "Failed: install_interactive.bat - $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "Generated $count position-based installer files" -ForegroundColor Cyan
Write-Host "Location: $outputDirectory" -ForegroundColor Gray
