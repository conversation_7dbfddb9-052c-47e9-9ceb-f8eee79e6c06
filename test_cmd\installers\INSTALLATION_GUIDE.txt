========================================
CrowdStrike Installation Guide
BAOPROVIP - Complete Installation System
========================================

OVERVIEW:
This system provides multiple ways to install CrowdStrike based on your department and position.

========================================
INSTALLATION OPTIONS
========================================

OPTION 1: DEPARTMENT-BASED INSTALLATION (Simple)
Location: by_department/
Usage: If you know your department and want standard installation

Examples:
- IT staff → run install_IT-Administration.bat (gets EDR)
- Marketing staff → run install_Marketing.bat (gets AV)
- Accounting staff → run install_Accounting.bat (gets EDR)

OPTION 2: POSITION-BASED INSTALLATION (For Senior Roles)
Location: by_position/
Usage: If you are senior/manager in a department that normally gets AV

Examples:
- Marketing Manager → run install_Manager_EDR.bat (gets EDR)
- HR Team Lead → run install_TeamLead_EDR.bat (gets EDR)
- Business Development Senior → run install_Senior_EDR.bat (gets EDR)

OPTION 3: INTERACTIVE INSTALLATION (Flexible)
Location: by_position/install_interactive.bat
Usage: Choose both department and position for custom installation

========================================
WHO SHOULD USE WHICH OPTION?
========================================

USE OPTION 1 (Department-based) IF:
✓ You are regular staff in any department
✓ You want the standard installation for your department
✓ You don't have senior/management position

USE OPTION 2 (Position-based) IF:
✓ You are Team Lead, Manager, or Director
✓ You are Senior/Specialist Lv3 in any department
✓ Your department normally gets AV but you need EDR

USE OPTION 3 (Interactive) IF:
✓ You're not sure which option to use
✓ You want to specify both department and position
✓ You need custom configuration

========================================
DETAILED SCENARIOS
========================================

SCENARIO 1: Regular IT Staff
→ Use: by_department/install_IT-Administration.bat
→ Result: Gets EDR (IT is high-priority department)

SCENARIO 2: Marketing Manager
→ Use: by_position/install_Manager_EDR.bat
→ Input: Enter "Marketing" when prompted
→ Result: Gets EDR (Manager position overrides department default)

SCENARIO 3: Senior Developer in Payoo-X
→ Use: by_position/install_Senior_EDR.bat
→ Input: Enter "Payoo-X-and-Biz-Solutions" when prompted
→ Result: Gets EDR (Senior in DEV department)

SCENARIO 4: Regular Marketing Staff
→ Use: by_department/install_Marketing.bat
→ Result: Gets AV (Marketing is standard department)

SCENARIO 5: HR Team Lead
→ Use: by_position/install_TeamLead_EDR.bat
→ Input: Enter "HR-Admin" when prompted
→ Result: Gets EDR (Team Lead position)

SCENARIO 6: Not sure what to choose
→ Use: by_position/install_interactive.bat
→ Follow prompts to select department and position
→ Result: Gets appropriate installation based on selections

========================================
DEPARTMENT CLASSIFICATIONS
========================================

EDR DEPARTMENTS (Always get EDR):
- Account-System-Management
- Accounting
- Board-of-Directors
- Core-System
- Customer-Service
- Cyber-Security
- Database-Management
- Digi-Gift
- HR-Admin
- IT-Administration
- Legal-Compliance
- Quality-of-Service
- Technical-Operation

AV DEPARTMENTS (Get AV by default, EDR for senior positions):
- Bill-Payment-Project
- Business-Analysis
- Business-Development
- Collaborator
- Data-Exchange
- E-Commerce
- Fee-Control
- Financial-Service-Project
- HN-Branch
- Marketing
- Mobile-App
- Network-Development
- NTT
- Omni
- Paycode
- Payoo-Plus-Digital-Transformation
- Payoo-X-and-Biz-Solutions
- Product-Management
- Project-Strategy-Division
- Quality-Control
- Service-Operation-Division
- System-Integration
- Training
- Web

========================================
POSITION HIERARCHY
========================================

MANAGEMENT POSITIONS (Always get EDR):
- Director
- Manager
- Team Lead

SENIOR POSITIONS (Get EDR in most cases):
- Senior Specialist
- Specialist Level 3

REGULAR POSITIONS (Follow department default):
- Staff
- Junior
- Intern

========================================
QUICK REFERENCE
========================================

I am a...                          → Use this file:
Regular IT staff                   → by_department/install_IT-Administration.bat
Marketing Manager                  → by_position/install_Manager_EDR.bat
Senior Marketing Specialist       → by_position/install_Senior_EDR.bat
Regular Marketing staff           → by_department/install_Marketing.bat
Accounting Team Lead              → by_position/install_TeamLead_EDR.bat
Regular Accounting staff          → by_department/install_Accounting.bat
HR Director                       → by_position/install_Director_EDR.bat
Senior Developer (Payoo-X)        → by_position/install_Senior_EDR.bat
Regular Developer                 → by_department/install_[Department].bat
Not sure                          → by_position/install_interactive.bat

========================================
TROUBLESHOOTING
========================================

PROBLEM: "Core script not found"
SOLUTION: Make sure you're running from the correct directory

PROBLEM: "Department not found"
SOLUTION: Check spelling, use exact department names from the list

PROBLEM: Installation fails
SOLUTION: 
1. Run as Administrator
2. Check internet connection
3. Check log file: C:\Temp\FalconInstall.log

PROBLEM: Not sure which department name to use
SOLUTION: Check DEPARTMENT_LIST.txt for exact names

========================================
VERIFICATION
========================================

After installation, verify success:
1. Run: tools\check_status.bat
2. Or manually check: sc.exe query csagent
3. Expected result: STATE: 4 RUNNING

========================================
SUPPORT
========================================

For technical issues:
- Check log file: C:\Temp\FalconInstall.log
- Contact IT Administration
- Use tools\check_status.bat for diagnostics

For questions about which installation to use:
- Refer to this guide
- Contact your department manager
- Use install_interactive.bat if unsure

========================================
VERSION INFO
========================================

System Version: 1.0
Last Updated: 2025-08-07
Total Department Files: 37
Total Position Files: 6
Interactive Files: 1
