@echo off
setlocal enabledelayedexpansion

:: Set colors
for /f %%A in ('echo prompt $E ^| cmd') do set "ESC=%%A"
set "GREEN=%ESC%[32m"
set "RED=%ESC%[31m"
set "YELLOW=%ESC%[33m"
set "BLUE=%ESC%[34m"
set "CYAN=%ESC%[36m"
set "WHITE=%ESC%[37m"
set "RESET=%ESC%[0m"

title CrowdStrike EDR Installation - Product-Management

echo %CYAN%========================================%RESET%
echo %CYAN%  CrowdStrike EDR Installation%RESET%
echo %CYAN%  Ph??ng Qu???n L?? S???n Ph???m%RESET%
echo %CYAN%========================================%RESET%
echo.
echo %WHITE%Department: Product-Management%RESET%
echo %WHITE%Description: Ph??ng Qu???n L?? S???n Ph???m%RESET%
echo %WHITE%Install Type: EDR (Enhanced Security)%RESET%
echo %WHITE%Target Users: Manager, Team Lead, Senior Staff%RESET%
echo.

echo %YELLOW%This is the EDR version for senior positions in Product-Management%RESET%
echo %WHITE%- Use this if you are: Manager, Team Lead, Senior, or Specialist Lv3%RESET%
echo %WHITE%- For regular staff, use: install_Product-Management.bat (AV version)%RESET%
echo.

set /p "CONFIRM=Are you a senior position (Manager/TeamLead/Senior)? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo.
    echo %YELLOW%For regular staff positions, please use the AV version:%RESET%
    echo %WHITE%File: install_Product-Management.bat%RESET%
    echo.
    pause
    exit /b 0
)

:: Check core script
set "CORE_SCRIPT=%~dp0..\..\core\auto_installer.ps1"
if not exist "%CORE_SCRIPT%" (
    echo %RED%ERROR: Core script not found at %CORE_SCRIPT%%RESET%
    pause
    exit /b 1
)

:: Run installation
echo.
echo %BLUE%Starting EDR installation for Product-Management...%RESET%
powershell -ExecutionPolicy Bypass -File "%CORE_SCRIPT%" -Department "Product-Management" -ForceInstallType "EDR" -Silent

echo.
if %errorlevel% equ 0 (
    echo %GREEN%EDR Installation completed successfully!%RESET%
    echo %WHITE%Department: Product-Management%RESET%
    echo %WHITE%Type: EDR (Enhanced Security)%RESET%
) else (
    echo %RED%Installation failed%RESET%
    echo %YELLOW%Please check the log file for details%RESET%
)

echo.
echo %CYAN%Next steps:%RESET%
echo %WHITE%1. Verify service: sc.exe query csagent%RESET%
echo %WHITE%2. Check log: C:\Temp\FalconInstall.log%RESET%
echo.

pause
