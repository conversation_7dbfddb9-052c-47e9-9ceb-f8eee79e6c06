# CrowdStrike Auto Installation Tool

## Mô tả
Tool tự động cài đặt CrowdStrike Falcon Sensor theo từng phòng ban với cấu hình được định nghĩa sẵn.

## C<PERSON>u trúc thư mục
```
test_cmd/
├── run_auto_crowstrike.bat          # File khởi chạy chính
├── run_auto_crowstrike.ps1          # Script PowerShell chính
├── department_config.json           # Cấu hình phòng ban
├── README_auto_install.md           # Hướng dẫn này
└── FalconSensor_Windows_installer (All AV)/
    ├── FalconSensor_Windows.exe     # File cài đặt CrowdStrike
    └── install_script.ps1           # Script cài đặt gốc
```

## Cách sử dụng

### 1. Khởi chạy nhanh (Recommended)
```batch
# Chạy file batch để có giao diện menu
run_auto_crowstrike.bat
```

### 2. Chạy PowerShell trực tiếp

#### Interactive Mode (<PERSON>ọn phòng ban từ danh sách)
```powershell
powershell -ExecutionPolicy Bypass .\run_auto_crowstrike.ps1
```

#### Silent Mode (Cài đặt tự động cho phòng ban cụ thể)
```powershell
powershell -ExecutionPolicy Bypass .\run_auto_crowstrike.ps1 -Department "IT-Administration" -Silent
```

#### Liệt kê tất cả phòng ban
```powershell
powershell -ExecutionPolicy Bypass .\run_auto_crowstrike.ps1 -ListDepartments
```

#### Chỉ định loại cài đặt
```powershell
powershell -ExecutionPolicy Bypass .\run_auto_crowstrike.ps1 -Department "Marketing" -InstallType "CS EDR"
```

## Tham số dòng lệnh

| Tham số | Mô tả | Ví dụ |
|---------|-------|-------|
| `-Department` | Tên phòng ban (chính xác) | `-Department "IT-Administration"` |
| `-InstallType` | Loại cài đặt (CS AV hoặc CS EDR) | `-InstallType "CS EDR"` |
| `-Silent` | Chạy không cần xác nhận | `-Silent` |
| `-ListDepartments` | Hiển thị danh sách phòng ban | `-ListDepartments` |

## Cấu hình phòng ban

### Phân loại theo mức độ ưu tiên:

#### High Priority (CS EDR)
- Board-of-Directors
- Legal-Compliance  
- Accounting
- Fee-Control
- Data-Exchange
- Account-System-Management
- Financial-Service-Project
- Bill-Payment-Project
- Paycode
- Web
- System-Integration
- Core-System
- Database-Management
- Quality-Control
- Mobile-App
- IT-Administration
- Technical-Operation
- Cyber-Security

#### Medium Priority (CS AV)
- HR-Admin
- Marketing
- HN-Branch
- Service-Operation-Division
- Customer-Service
- Quality-of-Service
- Project-Strategy-Division
- Business-Development
- Network-Development
- E-Commerce
- Omni
- Digi-Gift
- Product-Management
- Payoo-X-and-Biz-Solutions
- Business-Analysis
- Payoo-Plus-Digital-Transformation
- NTT

#### Low Priority (CS AV)
- Training
- Collaborator

## Loại cài đặt

### CS AV (CrowdStrike Antivirus)
- CID: `335D602035914F1CAF1319063B696DF5-D8`
- Dành cho: Phòng ban có mức độ rủi ro thấp đến trung bình

### CS EDR (CrowdStrike Endpoint Detection and Response)
- CID: `F1E595107CDA48009C1C668EBB7A7211-83`
- Dành cho: Phòng ban có mức độ rủi ro cao, xử lý dữ liệu nhạy cảm

## Log và Troubleshooting

### File log
- Đường dẫn: `C:\Temp\FalconInstall_Auto.log`
- Chứa toàn bộ quá trình cài đặt

### Kiểm tra trạng thái cài đặt
```cmd
sc.exe query csagent
```
Kết quả mong đợi: `STATE: 4 RUNNING`

### Thời gian cài đặt
- Thành công: ≤ 10 phút
- Cảnh báo: > 10 phút (cần kiểm tra thủ công)

## Yêu cầu hệ thống

1. **Quyền Administrator**: Script tự động yêu cầu quyền admin
2. **PowerShell**: Phiên bản 3.0 trở lên
3. **File cài đặt**: `FalconSensor_Windows.exe` phải có trong thư mục installer
4. **Kết nối mạng**: Để tải xuống và kích hoạt

## Ví dụ sử dụng

### Cài đặt cho phòng IT
```batch
# Chạy interactive mode
run_auto_crowstrike.bat
# Chọn option 1, sau đó chọn IT-Administration từ danh sách
```

### Cài đặt hàng loạt (Silent)
```powershell
# Cài đặt cho nhiều phòng ban
$departments = @("IT-Administration", "Cyber-Security", "Core-System")
foreach ($dept in $departments) {
    powershell -ExecutionPolicy Bypass .\run_auto_crowstrike.ps1 -Department $dept -Silent
}
```

### Kiểm tra danh sách phòng ban
```powershell
powershell -ExecutionPolicy Bypass .\run_auto_crowstrike.ps1 -ListDepartments
```

## Lưu ý quan trọng

1. **Backup**: Tạo backup hệ thống trước khi cài đặt
2. **Test**: Thử nghiệm trên máy test trước khi triển khai
3. **Network**: Đảm bảo kết nối internet ổn định
4. **Antivirus**: Tạm thời tắt antivirus khác nếu có xung đột
5. **Reboot**: Có thể cần khởi động lại sau khi cài đặt

## Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra file log tại `C:\Temp\FalconInstall_Auto.log`
2. Chạy lệnh kiểm tra service: `sc.exe query csagent`
3. Liên hệ team IT để được hỗ trợ

---
**Phát triển bởi**: BAOPROVIP Team  
**Phiên bản**: 1.0  
**Cập nhật**: 2025-08-07
